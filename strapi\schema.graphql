### This file was generated by Nexus Schema
### Do not make changes to this file directly


type AdminGroup {
  adminGroupId: String
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  contentManagedBy: String
  createdAt: DateTime
  documentId: ID!
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Document]!
  documents_connection(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): DocumentRelationResponseCollection
  faqs(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Faq]!
  faqs_connection(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqRelationResponseCollection
  landingPage: Page
  pages(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  pages_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type AdminGroupEntity {
  attributes: AdminGroup
  id: ID
}

type AdminGroupEntityResponse {
  data: AdminGroup
}

type AdminGroupEntityResponseCollection {
  nodes: [AdminGroup!]!
  pageInfo: Pagination!
}

input AdminGroupFiltersInput {
  adminGroupId: StringFilterInput
  and: [AdminGroupFiltersInput]
  articles: ArticleFiltersInput
  contentManagedBy: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  documents: DocumentFiltersInput
  faqs: FaqFiltersInput
  landingPage: PageFiltersInput
  not: AdminGroupFiltersInput
  or: [AdminGroupFiltersInput]
  pages: PageFiltersInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input AdminGroupInput {
  adminGroupId: String
  articles: [ID]
  contentManagedBy: String
  documents: [ID]
  faqs: [ID]
  landingPage: ID
  pages: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type AdminGroupRelationResponseCollection {
  nodes: [AdminGroup!]!
}

type Alert {
  createdAt: DateTime
  documentId: ID!
  locale: String
  localizations: [Alert]!
  localizations_connection: AlertRelationResponseCollection
  publishedAt: DateTime
  text: String
  updatedAt: DateTime
}

type AlertEntity {
  attributes: Alert
  id: ID
}

type AlertEntityResponse {
  data: Alert
}

type AlertEntityResponseCollection {
  nodes: [Alert!]!
  pageInfo: Pagination!
}

input AlertFiltersInput {
  and: [AlertFiltersInput]
  createdAt: DateTimeFilterInput
  locale: StringFilterInput
  localizations: AlertFiltersInput
  not: AlertFiltersInput
  or: [AlertFiltersInput]
  publishedAt: DateTimeFilterInput
  text: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input AlertInput {
  publishedAt: DateTime
  text: String
}

type AlertRelationResponseCollection {
  nodes: [Alert!]!
}

type Article {
  addedAt: DateTime!
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  alias: String
  articleCategory: ArticleCategory
  content: String
  coverMedia: UploadFile
  createdAt: DateTime
  documentId: ID!
  files(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  gallery(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UploadFile]!
  gallery_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection
  inbaRelease: InbaRelease
  locale: String
  localizations(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  localizations_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  slug: String!
  tag: Tag
  tags(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Tag]!
  tags_connection(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  title: String!
  updatedAt: DateTime
}

type ArticleCategory {
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  createdAt: DateTime
  documentId: ID!
  locale: String
  localizations(filters: ArticleCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ArticleCategory]!
  localizations_connection(filters: ArticleCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleCategoryRelationResponseCollection
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type ArticleCategoryEntity {
  attributes: ArticleCategory
  id: ID
}

type ArticleCategoryEntityResponse {
  data: ArticleCategory
}

type ArticleCategoryEntityResponseCollection {
  nodes: [ArticleCategory!]!
  pageInfo: Pagination!
}

input ArticleCategoryFiltersInput {
  and: [ArticleCategoryFiltersInput]
  articles: ArticleFiltersInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  locale: StringFilterInput
  localizations: ArticleCategoryFiltersInput
  not: ArticleCategoryFiltersInput
  or: [ArticleCategoryFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input ArticleCategoryInput {
  articles: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type ArticleCategoryRelationResponseCollection {
  nodes: [ArticleCategory!]!
}

type ArticleEntity {
  attributes: Article
  id: ID
}

type ArticleEntityResponse {
  data: Article
}

type ArticleEntityResponseCollection {
  nodes: [Article!]!
  pageInfo: Pagination!
}

input ArticleFiltersInput {
  addedAt: DateTimeFilterInput
  adminGroups: AdminGroupFiltersInput
  alias: StringFilterInput
  and: [ArticleFiltersInput]
  articleCategory: ArticleCategoryFiltersInput
  content: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  files: ComponentBlocksFileFiltersInput
  inbaRelease: InbaReleaseFiltersInput
  locale: StringFilterInput
  localizations: ArticleFiltersInput
  not: ArticleFiltersInput
  or: [ArticleFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  tag: TagFiltersInput
  tags: TagFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input ArticleInput {
  addedAt: DateTime
  adminGroups: [ID]
  alias: String
  articleCategory: ID
  content: String
  coverMedia: ID
  files: [ComponentBlocksFileInput]
  gallery: [ID]
  inbaRelease: ID
  perex: String
  publishedAt: DateTime
  slug: String
  tag: ID
  tags: [ID]
  title: String
}

type ArticleRelationResponseCollection {
  nodes: [Article!]!
}

input BooleanFilterInput {
  and: [Boolean]
  between: [Boolean]
  contains: Boolean
  containsi: Boolean
  endsWith: Boolean
  eq: Boolean
  eqi: Boolean
  gt: Boolean
  gte: Boolean
  in: [Boolean]
  lt: Boolean
  lte: Boolean
  ne: Boolean
  nei: Boolean
  not: BooleanFilterInput
  notContains: Boolean
  notContainsi: Boolean
  notIn: [Boolean]
  notNull: Boolean
  null: Boolean
  or: [Boolean]
  startsWith: Boolean
}

type ComponentAccordionItemsFlatText {
  category: String
  content: String
  fileList(filters: ComponentBlocksFileItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFileItem]
  id: ID!
  moreLinkPage: Page
  moreLinkTitle: String
  moreLinkUrl: String
}

input ComponentAccordionItemsFlatTextFiltersInput {
  and: [ComponentAccordionItemsFlatTextFiltersInput]
  category: StringFilterInput
  content: StringFilterInput
  fileList: ComponentBlocksFileItemFiltersInput
  moreLinkPage: PageFiltersInput
  moreLinkTitle: StringFilterInput
  moreLinkUrl: StringFilterInput
  not: ComponentAccordionItemsFlatTextFiltersInput
  or: [ComponentAccordionItemsFlatTextFiltersInput]
}

input ComponentAccordionItemsFlatTextInput {
  category: String
  content: String
  fileList: [ComponentBlocksFileItemInput]
  id: ID
  moreLinkPage: ID
  moreLinkTitle: String
  moreLinkUrl: String
}

type ComponentAccordionItemsInstitution {
  category: String
  firstColumn: String
  id: ID!
  secondColumn: String
  subtitle: String
  thirdColumn: String
  title: String
  url: String
  urlLabel: String
}

input ComponentAccordionItemsInstitutionFiltersInput {
  and: [ComponentAccordionItemsInstitutionFiltersInput]
  category: StringFilterInput
  firstColumn: StringFilterInput
  not: ComponentAccordionItemsInstitutionFiltersInput
  or: [ComponentAccordionItemsInstitutionFiltersInput]
  secondColumn: StringFilterInput
  subtitle: StringFilterInput
  thirdColumn: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
  urlLabel: StringFilterInput
}

input ComponentAccordionItemsInstitutionInput {
  category: String
  firstColumn: String
  id: ID
  secondColumn: String
  subtitle: String
  thirdColumn: String
  title: String
  url: String
  urlLabel: String
}

type ComponentAccordionItemsInstitutionNarrow {
  category: String
  id: ID!
  subtitle: String
  title: String
  url: String
  urlLabel: String
}

input ComponentAccordionItemsInstitutionNarrowFiltersInput {
  and: [ComponentAccordionItemsInstitutionNarrowFiltersInput]
  category: StringFilterInput
  not: ComponentAccordionItemsInstitutionNarrowFiltersInput
  or: [ComponentAccordionItemsInstitutionNarrowFiltersInput]
  subtitle: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
  urlLabel: StringFilterInput
}

input ComponentAccordionItemsInstitutionNarrowInput {
  category: String
  id: ID
  subtitle: String
  title: String
  url: String
  urlLabel: String
}

type ComponentBlocksCardLink {
  analyticsId: String
  article: Article
  id: ID!
  label: String
  media: UploadFile
  page: Page
  subtext: String
  url: String
}

input ComponentBlocksCardLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksCardLinkFiltersInput]
  article: ArticleFiltersInput
  label: StringFilterInput
  not: ComponentBlocksCardLinkFiltersInput
  or: [ComponentBlocksCardLinkFiltersInput]
  page: PageFiltersInput
  subtext: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksCardLinkInput {
  analyticsId: String
  article: ID
  id: ID
  label: String
  media: ID
  page: ID
  subtext: String
  url: String
}

type ComponentBlocksColumnsItem {
  id: ID!
  image: UploadFile
  text: String
  title: String
}

input ComponentBlocksColumnsItemFiltersInput {
  and: [ComponentBlocksColumnsItemFiltersInput]
  not: ComponentBlocksColumnsItemFiltersInput
  or: [ComponentBlocksColumnsItemFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentBlocksColumnsItemInput {
  id: ID
  image: ID
  text: String
  title: String
}

type ComponentBlocksCommonLink {
  analyticsId: String
  article: Article
  id: ID!
  label: String
  page: Page
  url: String
}

input ComponentBlocksCommonLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksCommonLinkFiltersInput]
  article: ArticleFiltersInput
  label: StringFilterInput
  not: ComponentBlocksCommonLinkFiltersInput
  or: [ComponentBlocksCommonLinkFiltersInput]
  page: PageFiltersInput
  url: StringFilterInput
}

input ComponentBlocksCommonLinkInput {
  analyticsId: String
  article: ID
  id: ID
  label: String
  page: ID
  url: String
}

type ComponentBlocksComparisonCard {
  iconMedia: UploadFile
  id: ID!
  items(filters: ComponentBlocksComparisonItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonItem]!
  title: String!
}

input ComponentBlocksComparisonCardFiltersInput {
  and: [ComponentBlocksComparisonCardFiltersInput]
  items: ComponentBlocksComparisonItemFiltersInput
  not: ComponentBlocksComparisonCardFiltersInput
  or: [ComponentBlocksComparisonCardFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksComparisonCardInput {
  iconMedia: ID
  id: ID
  items: [ComponentBlocksComparisonItemInput]
  title: String
}

type ComponentBlocksComparisonItem {
  id: ID!
  label: String!
}

input ComponentBlocksComparisonItemFiltersInput {
  and: [ComponentBlocksComparisonItemFiltersInput]
  label: StringFilterInput
  not: ComponentBlocksComparisonItemFiltersInput
  or: [ComponentBlocksComparisonItemFiltersInput]
}

input ComponentBlocksComparisonItemInput {
  id: ID
  label: String
}

type ComponentBlocksContactCard {
  id: ID!
  overrideLabel: String
  value: String!
}

input ComponentBlocksContactCardFiltersInput {
  and: [ComponentBlocksContactCardFiltersInput]
  not: ComponentBlocksContactCardFiltersInput
  or: [ComponentBlocksContactCardFiltersInput]
  overrideLabel: StringFilterInput
  value: StringFilterInput
}

input ComponentBlocksContactCardInput {
  id: ID
  overrideLabel: String
  value: String
}

type ComponentBlocksContactDirectionsCard {
  address: String!
  barrierFreeInfo: String
  id: ID!
  iframeUrl: String
  overrideLabel: String
  parkingInfo: String
  publicTransportInfo: String
}

input ComponentBlocksContactDirectionsCardFiltersInput {
  address: StringFilterInput
  and: [ComponentBlocksContactDirectionsCardFiltersInput]
  barrierFreeInfo: StringFilterInput
  iframeUrl: StringFilterInput
  not: ComponentBlocksContactDirectionsCardFiltersInput
  or: [ComponentBlocksContactDirectionsCardFiltersInput]
  overrideLabel: StringFilterInput
  parkingInfo: StringFilterInput
  publicTransportInfo: StringFilterInput
}

input ComponentBlocksContactDirectionsCardInput {
  address: String
  barrierFreeInfo: String
  id: ID
  iframeUrl: String
  overrideLabel: String
  parkingInfo: String
  publicTransportInfo: String
}

type ComponentBlocksContactPersonCard {
  email: String
  id: ID!
  phone: String
  subtext: String
  title: String!
}

input ComponentBlocksContactPersonCardFiltersInput {
  and: [ComponentBlocksContactPersonCardFiltersInput]
  email: StringFilterInput
  not: ComponentBlocksContactPersonCardFiltersInput
  or: [ComponentBlocksContactPersonCardFiltersInput]
  phone: StringFilterInput
  subtext: StringFilterInput
  title: StringFilterInput
}

input ComponentBlocksContactPersonCardInput {
  email: String
  id: ID
  phone: String
  subtext: String
  title: String
}

type ComponentBlocksFile {
  id: ID!
  media: UploadFile
  title: String
}

input ComponentBlocksFileFiltersInput {
  and: [ComponentBlocksFileFiltersInput]
  not: ComponentBlocksFileFiltersInput
  or: [ComponentBlocksFileFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFileInput {
  id: ID
  media: ID
  title: String
}

type ComponentBlocksFileItem {
  id: ID!
  media: UploadFile!
  title: String
}

input ComponentBlocksFileItemFiltersInput {
  and: [ComponentBlocksFileItemFiltersInput]
  not: ComponentBlocksFileItemFiltersInput
  or: [ComponentBlocksFileItemFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFileItemInput {
  id: ID
  media: ID
  title: String
}

type ComponentBlocksFooterColumn {
  id: ID!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
  title: String!
}

input ComponentBlocksFooterColumnFiltersInput {
  and: [ComponentBlocksFooterColumnFiltersInput]
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentBlocksFooterColumnFiltersInput
  or: [ComponentBlocksFooterColumnFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksFooterColumnInput {
  id: ID
  links: [ComponentBlocksCommonLinkInput]
  title: String
}

type ComponentBlocksHomepageHighlightsItem {
  analyticsId: String
  article: Article
  id: ID!
  label: String
  media: UploadFile
  page: Page
  subtext: String
  url: String
}

input ComponentBlocksHomepageHighlightsItemFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksHomepageHighlightsItemFiltersInput]
  article: ArticleFiltersInput
  label: StringFilterInput
  not: ComponentBlocksHomepageHighlightsItemFiltersInput
  or: [ComponentBlocksHomepageHighlightsItemFiltersInput]
  page: PageFiltersInput
  subtext: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksHomepageHighlightsItemInput {
  analyticsId: String
  article: ID
  id: ID
  label: String
  media: ID
  page: ID
  subtext: String
  url: String
}

type ComponentBlocksInBa {
  content: String
  id: ID!
  showMoreLink: ComponentBlocksCommonLink
  title: String
}

input ComponentBlocksInBaFiltersInput {
  and: [ComponentBlocksInBaFiltersInput]
  content: StringFilterInput
  not: ComponentBlocksInBaFiltersInput
  or: [ComponentBlocksInBaFiltersInput]
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  title: StringFilterInput
}

input ComponentBlocksInBaInput {
  content: String
  id: ID
  showMoreLink: ComponentBlocksCommonLinkInput
  title: String
}

type ComponentBlocksNumbersOverviewItem {
  id: ID!
  number: String!
  text: String!
}

input ComponentBlocksNumbersOverviewItemFiltersInput {
  and: [ComponentBlocksNumbersOverviewItemFiltersInput]
  not: ComponentBlocksNumbersOverviewItemFiltersInput
  number: StringFilterInput
  or: [ComponentBlocksNumbersOverviewItemFiltersInput]
  text: StringFilterInput
}

input ComponentBlocksNumbersOverviewItemInput {
  id: ID
  number: String
  text: String
}

type ComponentBlocksNumericalListItem {
  id: ID!
  text: String
}

input ComponentBlocksNumericalListItemFiltersInput {
  and: [ComponentBlocksNumericalListItemFiltersInput]
  not: ComponentBlocksNumericalListItemFiltersInput
  or: [ComponentBlocksNumericalListItemFiltersInput]
  text: StringFilterInput
}

input ComponentBlocksNumericalListItemInput {
  id: ID
  text: String
}

type ComponentBlocksOpeningHoursAlertMessage {
  id: ID!
  text: String
}

input ComponentBlocksOpeningHoursAlertMessageFiltersInput {
  and: [ComponentBlocksOpeningHoursAlertMessageFiltersInput]
  not: ComponentBlocksOpeningHoursAlertMessageFiltersInput
  or: [ComponentBlocksOpeningHoursAlertMessageFiltersInput]
  text: StringFilterInput
}

input ComponentBlocksOpeningHoursAlertMessageInput {
  id: ID
  text: String
}

type ComponentBlocksOpeningHoursItem {
  id: ID!
  label: String!
  value: String!
}

input ComponentBlocksOpeningHoursItemFiltersInput {
  and: [ComponentBlocksOpeningHoursItemFiltersInput]
  label: StringFilterInput
  not: ComponentBlocksOpeningHoursItemFiltersInput
  or: [ComponentBlocksOpeningHoursItemFiltersInput]
  value: StringFilterInput
}

input ComponentBlocksOpeningHoursItemInput {
  id: ID
  label: String
  value: String
}

type ComponentBlocksPageLink {
  analyticsId: String
  id: ID!
  page: Page
  title: String
  url: String
}

input ComponentBlocksPageLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksPageLinkFiltersInput]
  not: ComponentBlocksPageLinkFiltersInput
  or: [ComponentBlocksPageLinkFiltersInput]
  page: PageFiltersInput
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksPageLinkInput {
  analyticsId: String
  id: ID
  page: ID
  title: String
  url: String
}

type ComponentBlocksPartner {
  id: ID!
  logo: UploadFile!
  title: String!
  url: String
}

input ComponentBlocksPartnerFiltersInput {
  and: [ComponentBlocksPartnerFiltersInput]
  not: ComponentBlocksPartnerFiltersInput
  or: [ComponentBlocksPartnerFiltersInput]
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksPartnerInput {
  id: ID
  logo: ID
  title: String
  url: String
}

type ComponentBlocksProsAndConsCard {
  id: ID!
  items(filters: ComponentBlocksComparisonItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonItem]!
  title: String!
}

input ComponentBlocksProsAndConsCardFiltersInput {
  and: [ComponentBlocksProsAndConsCardFiltersInput]
  items: ComponentBlocksComparisonItemFiltersInput
  not: ComponentBlocksProsAndConsCardFiltersInput
  or: [ComponentBlocksProsAndConsCardFiltersInput]
  title: StringFilterInput
}

input ComponentBlocksProsAndConsCardInput {
  id: ID
  items: [ComponentBlocksComparisonItemInput]
  title: String
}

type ComponentBlocksStarzLandingPageBanner {
  content: String
  contentPosition: ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_CONTENTPOSITION!
  id: ID!
  media: UploadFile!
  primaryLink: ComponentBlocksCommonLink
  secondaryLink: ComponentBlocksCommonLink
  tertiaryLink: ComponentBlocksCommonLink
  title: String!
  variant: ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_VARIANT!
}

input ComponentBlocksStarzLandingPageBannerFiltersInput {
  and: [ComponentBlocksStarzLandingPageBannerFiltersInput]
  content: StringFilterInput
  contentPosition: StringFilterInput
  not: ComponentBlocksStarzLandingPageBannerFiltersInput
  or: [ComponentBlocksStarzLandingPageBannerFiltersInput]
  primaryLink: ComponentBlocksCommonLinkFiltersInput
  secondaryLink: ComponentBlocksCommonLinkFiltersInput
  tertiaryLink: ComponentBlocksCommonLinkFiltersInput
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentBlocksStarzLandingPageBannerInput {
  content: String
  contentPosition: ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_CONTENTPOSITION
  id: ID
  media: ID
  primaryLink: ComponentBlocksCommonLinkInput
  secondaryLink: ComponentBlocksCommonLinkInput
  tertiaryLink: ComponentBlocksCommonLinkInput
  title: String
  variant: ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_VARIANT
}

type ComponentBlocksSubnavigationLink {
  analyticsId: String
  id: ID!
  label: String
  page: Page
  subtext: String
  url: String
}

input ComponentBlocksSubnavigationLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentBlocksSubnavigationLinkFiltersInput]
  label: StringFilterInput
  not: ComponentBlocksSubnavigationLinkFiltersInput
  or: [ComponentBlocksSubnavigationLinkFiltersInput]
  page: PageFiltersInput
  subtext: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksSubnavigationLinkInput {
  analyticsId: String
  id: ID
  label: String
  page: ID
  subtext: String
  url: String
}

type ComponentBlocksTopServicesItem {
  icon: ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON!
  id: ID!
  link: ComponentBlocksCommonLink!
}

input ComponentBlocksTopServicesItemFiltersInput {
  and: [ComponentBlocksTopServicesItemFiltersInput]
  icon: StringFilterInput
  link: ComponentBlocksCommonLinkFiltersInput
  not: ComponentBlocksTopServicesItemFiltersInput
  or: [ComponentBlocksTopServicesItemFiltersInput]
}

input ComponentBlocksTopServicesItemInput {
  icon: ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON
  id: ID
  link: ComponentBlocksCommonLinkInput
}

type ComponentBlocksVideo {
  id: ID!
  speaker: String
  title: String
  url: String!
}

input ComponentBlocksVideoFiltersInput {
  and: [ComponentBlocksVideoFiltersInput]
  not: ComponentBlocksVideoFiltersInput
  or: [ComponentBlocksVideoFiltersInput]
  speaker: StringFilterInput
  title: StringFilterInput
  url: StringFilterInput
}

input ComponentBlocksVideoInput {
  id: ID
  speaker: String
  title: String
  url: String
}

type ComponentGeneralHeader {
  accountLink: ComponentBlocksCommonLink
  id: ID!
  links(filters: ComponentGeneralHeaderLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentGeneralHeaderLink]
}

input ComponentGeneralHeaderFiltersInput {
  accountLink: ComponentBlocksCommonLinkFiltersInput
  and: [ComponentGeneralHeaderFiltersInput]
  links: ComponentGeneralHeaderLinkFiltersInput
  not: ComponentGeneralHeaderFiltersInput
  or: [ComponentGeneralHeaderFiltersInput]
}

input ComponentGeneralHeaderInput {
  accountLink: ComponentBlocksCommonLinkInput
  id: ID
  links: [ComponentGeneralHeaderLinkInput]
}

type ComponentGeneralHeaderLink {
  analyticsId: String
  icon: ENUM_COMPONENTGENERALHEADERLINK_ICON!
  id: ID!
  label: String
  page: Page
  showOnDesktop: Boolean!
  showOnMobile: Boolean!
  url: String
}

input ComponentGeneralHeaderLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentGeneralHeaderLinkFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  not: ComponentGeneralHeaderLinkFiltersInput
  or: [ComponentGeneralHeaderLinkFiltersInput]
  page: PageFiltersInput
  showOnDesktop: BooleanFilterInput
  showOnMobile: BooleanFilterInput
  url: StringFilterInput
}

input ComponentGeneralHeaderLinkInput {
  analyticsId: String
  icon: ENUM_COMPONENTGENERALHEADERLINK_ICON
  id: ID
  label: String
  page: ID
  showOnDesktop: Boolean
  showOnMobile: Boolean
  url: String
}

type ComponentHeaderSectionsEvent {
  address: String
  date: Date
  id: ID!
}

input ComponentHeaderSectionsEventFiltersInput {
  address: StringFilterInput
  and: [ComponentHeaderSectionsEventFiltersInput]
  date: DateFilterInput
  not: ComponentHeaderSectionsEventFiltersInput
  or: [ComponentHeaderSectionsEventFiltersInput]
}

input ComponentHeaderSectionsEventInput {
  address: String
  date: Date
  id: ID
}

type ComponentHeaderSectionsFacility {
  address: String
  id: ID!
  media(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UploadFile]!
  media_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection!
  navigateToLink: String
}

input ComponentHeaderSectionsFacilityFiltersInput {
  address: StringFilterInput
  and: [ComponentHeaderSectionsFacilityFiltersInput]
  navigateToLink: StringFilterInput
  not: ComponentHeaderSectionsFacilityFiltersInput
  or: [ComponentHeaderSectionsFacilityFiltersInput]
}

input ComponentHeaderSectionsFacilityInput {
  address: String
  id: ID
  media: [ID]
  navigateToLink: String
}

type ComponentMenuMenuItem {
  icon: ENUM_COMPONENTMENUMENUITEM_ICON!
  id: ID!
  label: String!
  page: Page
  sections(filters: ComponentMenuMenuSectionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuSection]
}

input ComponentMenuMenuItemFiltersInput {
  and: [ComponentMenuMenuItemFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  not: ComponentMenuMenuItemFiltersInput
  or: [ComponentMenuMenuItemFiltersInput]
  page: PageFiltersInput
  sections: ComponentMenuMenuSectionFiltersInput
}

input ComponentMenuMenuItemInput {
  icon: ENUM_COMPONENTMENUMENUITEM_ICON
  id: ID
  label: String
  page: ID
  sections: [ComponentMenuMenuSectionInput]
}

type ComponentMenuMenuLink {
  analyticsId: String
  id: ID!
  label: String
  page: Page
  url: String
}

input ComponentMenuMenuLinkFiltersInput {
  analyticsId: StringFilterInput
  and: [ComponentMenuMenuLinkFiltersInput]
  label: StringFilterInput
  not: ComponentMenuMenuLinkFiltersInput
  or: [ComponentMenuMenuLinkFiltersInput]
  page: PageFiltersInput
  url: StringFilterInput
}

input ComponentMenuMenuLinkInput {
  analyticsId: String
  id: ID
  label: String
  page: ID
  url: String
}

type ComponentMenuMenuSection {
  icon: ENUM_COMPONENTMENUMENUSECTION_ICON!
  id: ID!
  label: String!
  links(filters: ComponentMenuMenuLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuLink]
  page: Page
  subtext: String
}

input ComponentMenuMenuSectionFiltersInput {
  and: [ComponentMenuMenuSectionFiltersInput]
  icon: StringFilterInput
  label: StringFilterInput
  links: ComponentMenuMenuLinkFiltersInput
  not: ComponentMenuMenuSectionFiltersInput
  or: [ComponentMenuMenuSectionFiltersInput]
  page: PageFiltersInput
  subtext: StringFilterInput
}

input ComponentMenuMenuSectionInput {
  icon: ENUM_COMPONENTMENUMENUSECTION_ICON
  id: ID
  label: String
  links: [ComponentMenuMenuLinkInput]
  page: ID
  subtext: String
}

type ComponentSectionsAccordion {
  flatText(filters: ComponentAccordionItemsFlatTextFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsFlatText]
  id: ID!
  institutions(filters: ComponentAccordionItemsInstitutionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsInstitution]
  institutionsNarrow(filters: ComponentAccordionItemsInstitutionNarrowFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentAccordionItemsInstitutionNarrow]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSACCORDION_TITLELEVEL
}

input ComponentSectionsAccordionFiltersInput {
  and: [ComponentSectionsAccordionFiltersInput]
  flatText: ComponentAccordionItemsFlatTextFiltersInput
  institutions: ComponentAccordionItemsInstitutionFiltersInput
  institutionsNarrow: ComponentAccordionItemsInstitutionNarrowFiltersInput
  not: ComponentSectionsAccordionFiltersInput
  or: [ComponentSectionsAccordionFiltersInput]
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsAccordionInput {
  flatText: [ComponentAccordionItemsFlatTextInput]
  id: ID
  institutions: [ComponentAccordionItemsInstitutionInput]
  institutionsNarrow: [ComponentAccordionItemsInstitutionNarrowInput]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSACCORDION_TITLELEVEL
}

type ComponentSectionsArticles {
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  articleCategories(filters: ArticleCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ArticleCategory]!
  articleCategories_connection(filters: ArticleCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleCategoryRelationResponseCollection
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  category: PageCategory
  id: ID!
  showAll: Boolean
  showMoreLink: ComponentBlocksCommonLink
  tags(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Tag]!
  tags_connection(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  text: String
  title: String
}

input ComponentSectionsArticlesFiltersInput {
  adminGroups: AdminGroupFiltersInput
  and: [ComponentSectionsArticlesFiltersInput]
  articleCategories: ArticleCategoryFiltersInput
  articles: ArticleFiltersInput
  category: PageCategoryFiltersInput
  not: ComponentSectionsArticlesFiltersInput
  or: [ComponentSectionsArticlesFiltersInput]
  showAll: BooleanFilterInput
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  tags: TagFiltersInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsArticlesInput {
  adminGroups: [ID]
  articleCategories: [ID]
  articles: [ID]
  category: ID
  id: ID
  showAll: Boolean
  showMoreLink: ComponentBlocksCommonLinkInput
  tags: [ID]
  text: String
  title: String
}

type ComponentSectionsBanner {
  content: String
  contentPosition: ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION!
  id: ID!
  media: UploadFile!
  primaryLink: ComponentBlocksCommonLink
  secondaryLink: ComponentBlocksCommonLink
  tertiaryLink: ComponentBlocksCommonLink
  title: String!
  variant: ENUM_COMPONENTSECTIONSBANNER_VARIANT!
}

input ComponentSectionsBannerFiltersInput {
  and: [ComponentSectionsBannerFiltersInput]
  content: StringFilterInput
  contentPosition: StringFilterInput
  not: ComponentSectionsBannerFiltersInput
  or: [ComponentSectionsBannerFiltersInput]
  primaryLink: ComponentBlocksCommonLinkFiltersInput
  secondaryLink: ComponentBlocksCommonLinkFiltersInput
  tertiaryLink: ComponentBlocksCommonLinkFiltersInput
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentSectionsBannerInput {
  content: String
  contentPosition: ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION
  id: ID
  media: ID
  primaryLink: ComponentBlocksCommonLinkInput
  secondaryLink: ComponentBlocksCommonLinkInput
  tertiaryLink: ComponentBlocksCommonLinkInput
  title: String
  variant: ENUM_COMPONENTSECTIONSBANNER_VARIANT
}

type ComponentSectionsCalculator {
  another_adult_value: Float
  child_value: Float
  id: ID!
  single_adult_value: Float
}

input ComponentSectionsCalculatorFiltersInput {
  and: [ComponentSectionsCalculatorFiltersInput]
  another_adult_value: FloatFilterInput
  child_value: FloatFilterInput
  not: ComponentSectionsCalculatorFiltersInput
  or: [ComponentSectionsCalculatorFiltersInput]
  single_adult_value: FloatFilterInput
}

input ComponentSectionsCalculatorInput {
  another_adult_value: Float
  child_value: Float
  id: ID
  single_adult_value: Float
}

type ComponentSectionsColumnedText {
  content: String
  id: ID!
  title: String
}

input ComponentSectionsColumnedTextFiltersInput {
  and: [ComponentSectionsColumnedTextFiltersInput]
  content: StringFilterInput
  not: ComponentSectionsColumnedTextFiltersInput
  or: [ComponentSectionsColumnedTextFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsColumnedTextInput {
  content: String
  id: ID
  title: String
}

type ComponentSectionsColumns {
  columns(filters: ComponentBlocksColumnsItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksColumnsItem]!
  id: ID!
  imageVariant: ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT!
  responsiveLayout: ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT!
  text: String
  title: String
}

input ComponentSectionsColumnsFiltersInput {
  and: [ComponentSectionsColumnsFiltersInput]
  columns: ComponentBlocksColumnsItemFiltersInput
  imageVariant: StringFilterInput
  not: ComponentSectionsColumnsFiltersInput
  or: [ComponentSectionsColumnsFiltersInput]
  responsiveLayout: StringFilterInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsColumnsInput {
  columns: [ComponentBlocksColumnsItemInput]
  id: ID
  imageVariant: ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT
  responsiveLayout: ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT
  text: String
  title: String
}

type ComponentSectionsComparisonSection {
  cards(filters: ComponentBlocksComparisonCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksComparisonCard]!
  id: ID!
  text: String
  textAlign: ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN!
  title: String
}

input ComponentSectionsComparisonSectionFiltersInput {
  and: [ComponentSectionsComparisonSectionFiltersInput]
  cards: ComponentBlocksComparisonCardFiltersInput
  not: ComponentSectionsComparisonSectionFiltersInput
  or: [ComponentSectionsComparisonSectionFiltersInput]
  text: StringFilterInput
  textAlign: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsComparisonSectionInput {
  cards: [ComponentBlocksComparisonCardInput]
  id: ID
  text: String
  textAlign: ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN
  title: String
}

type ComponentSectionsContactsSection {
  addressContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  bankConnectionContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  billingInfoContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  description: String
  directionsContact: ComponentBlocksContactDirectionsCard
  emailContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  id: ID!
  openingHoursContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  personContacts(filters: ComponentBlocksContactPersonCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactPersonCard]
  phoneContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  postalAddressContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSCONTACTSSECTION_TITLELEVEL
  webContacts(filters: ComponentBlocksContactCardFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksContactCard]
}

input ComponentSectionsContactsSectionFiltersInput {
  addressContacts: ComponentBlocksContactCardFiltersInput
  and: [ComponentSectionsContactsSectionFiltersInput]
  bankConnectionContacts: ComponentBlocksContactCardFiltersInput
  billingInfoContacts: ComponentBlocksContactCardFiltersInput
  description: StringFilterInput
  directionsContact: ComponentBlocksContactDirectionsCardFiltersInput
  emailContacts: ComponentBlocksContactCardFiltersInput
  not: ComponentSectionsContactsSectionFiltersInput
  openingHoursContacts: ComponentBlocksContactCardFiltersInput
  or: [ComponentSectionsContactsSectionFiltersInput]
  personContacts: ComponentBlocksContactPersonCardFiltersInput
  phoneContacts: ComponentBlocksContactCardFiltersInput
  postalAddressContacts: ComponentBlocksContactCardFiltersInput
  title: StringFilterInput
  titleLevel: StringFilterInput
  webContacts: ComponentBlocksContactCardFiltersInput
}

input ComponentSectionsContactsSectionInput {
  addressContacts: [ComponentBlocksContactCardInput]
  bankConnectionContacts: [ComponentBlocksContactCardInput]
  billingInfoContacts: [ComponentBlocksContactCardInput]
  description: String
  directionsContact: ComponentBlocksContactDirectionsCardInput
  emailContacts: [ComponentBlocksContactCardInput]
  id: ID
  openingHoursContacts: [ComponentBlocksContactCardInput]
  personContacts: [ComponentBlocksContactPersonCardInput]
  phoneContacts: [ComponentBlocksContactCardInput]
  postalAddressContacts: [ComponentBlocksContactCardInput]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSCONTACTSSECTION_TITLELEVEL
  webContacts: [ComponentBlocksContactCardInput]
}

type ComponentSectionsDivider {
  id: ID!
  style: ENUM_COMPONENTSECTIONSDIVIDER_STYLE
}

input ComponentSectionsDividerFiltersInput {
  and: [ComponentSectionsDividerFiltersInput]
  not: ComponentSectionsDividerFiltersInput
  or: [ComponentSectionsDividerFiltersInput]
  style: StringFilterInput
}

input ComponentSectionsDividerInput {
  id: ID
  style: ENUM_COMPONENTSECTIONSDIVIDER_STYLE
}

type ComponentSectionsDocuments {
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Document]!
  documents_connection(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): DocumentRelationResponseCollection
  id: ID!
  showAll: Boolean
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSDOCUMENTS_TITLELEVEL
}

input ComponentSectionsDocumentsFiltersInput {
  and: [ComponentSectionsDocumentsFiltersInput]
  documents: DocumentFiltersInput
  not: ComponentSectionsDocumentsFiltersInput
  or: [ComponentSectionsDocumentsFiltersInput]
  showAll: BooleanFilterInput
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsDocumentsInput {
  documents: [ID]
  id: ID
  showAll: Boolean
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSDOCUMENTS_TITLELEVEL
}

type ComponentSectionsEvents {
  eventPages(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  eventPages_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  id: ID!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSEVENTS_TITLELEVEL
}

input ComponentSectionsEventsFiltersInput {
  and: [ComponentSectionsEventsFiltersInput]
  eventPages: PageFiltersInput
  not: ComponentSectionsEventsFiltersInput
  or: [ComponentSectionsEventsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsEventsInput {
  eventPages: [ID]
  id: ID
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSEVENTS_TITLELEVEL
}

type ComponentSectionsFacilities {
  facilityPages(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  facilityPages_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  id: ID!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFACILITIES_TITLELEVEL
}

input ComponentSectionsFacilitiesFiltersInput {
  and: [ComponentSectionsFacilitiesFiltersInput]
  facilityPages: PageFiltersInput
  not: ComponentSectionsFacilitiesFiltersInput
  or: [ComponentSectionsFacilitiesFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsFacilitiesInput {
  facilityPages: [ID]
  id: ID
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFACILITIES_TITLELEVEL
}

type ComponentSectionsFaqCategories {
  faqCategories(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [FaqCategory]!
  faqCategories_connection(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqCategoryRelationResponseCollection
  id: ID!
  text: String
  title: String
}

input ComponentSectionsFaqCategoriesFiltersInput {
  and: [ComponentSectionsFaqCategoriesFiltersInput]
  faqCategories: FaqCategoryFiltersInput
  not: ComponentSectionsFaqCategoriesFiltersInput
  or: [ComponentSectionsFaqCategoriesFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsFaqCategoriesInput {
  faqCategories: [ID]
  id: ID
  text: String
  title: String
}

type ComponentSectionsFaqs {
  faqs(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Faq]!
  faqs_connection(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqRelationResponseCollection
  id: ID!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFAQS_TITLELEVEL
}

input ComponentSectionsFaqsFiltersInput {
  and: [ComponentSectionsFaqsFiltersInput]
  faqs: FaqFiltersInput
  not: ComponentSectionsFaqsFiltersInput
  or: [ComponentSectionsFaqsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsFaqsInput {
  faqs: [ID]
  id: ID
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFAQS_TITLELEVEL
}

type ComponentSectionsFileList {
  fileList(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  id: ID!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFILELIST_TITLELEVEL
}

input ComponentSectionsFileListFiltersInput {
  and: [ComponentSectionsFileListFiltersInput]
  fileList: ComponentBlocksFileFiltersInput
  not: ComponentSectionsFileListFiltersInput
  or: [ComponentSectionsFileListFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsFileListInput {
  fileList: [ComponentBlocksFileInput]
  id: ID
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSFILELIST_TITLELEVEL
}

type ComponentSectionsGallery {
  id: ID!
  medias(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UploadFile]!
  medias_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSGALLERY_TITLELEVEL
}

input ComponentSectionsGalleryFiltersInput {
  and: [ComponentSectionsGalleryFiltersInput]
  not: ComponentSectionsGalleryFiltersInput
  or: [ComponentSectionsGalleryFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsGalleryInput {
  id: ID
  medias: [ID]
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSGALLERY_TITLELEVEL
}

type ComponentSectionsHomepageEvents {
  eventsPageLink: ComponentBlocksCommonLink
  id: ID!
  text: String
  title: String
}

input ComponentSectionsHomepageEventsFiltersInput {
  and: [ComponentSectionsHomepageEventsFiltersInput]
  eventsPageLink: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageEventsFiltersInput
  or: [ComponentSectionsHomepageEventsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageEventsInput {
  eventsPageLink: ComponentBlocksCommonLinkInput
  id: ID
  text: String
  title: String
}

type ComponentSectionsHomepageHighlights {
  cards(filters: ComponentBlocksHomepageHighlightsItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksHomepageHighlightsItem]
  id: ID!
  text: String
  title: String
}

input ComponentSectionsHomepageHighlightsFiltersInput {
  and: [ComponentSectionsHomepageHighlightsFiltersInput]
  cards: ComponentBlocksHomepageHighlightsItemFiltersInput
  not: ComponentSectionsHomepageHighlightsFiltersInput
  or: [ComponentSectionsHomepageHighlightsFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageHighlightsInput {
  cards: [ComponentBlocksHomepageHighlightsItemInput]
  id: ID
  text: String
  title: String
}

type ComponentSectionsHomepageMayorAndCouncil {
  councilCard: ComponentBlocksCommonLink
  id: ID!
  mayorCard: ComponentBlocksCommonLink
  text: String
  title: String
}

input ComponentSectionsHomepageMayorAndCouncilFiltersInput {
  and: [ComponentSectionsHomepageMayorAndCouncilFiltersInput]
  councilCard: ComponentBlocksCommonLinkFiltersInput
  mayorCard: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageMayorAndCouncilFiltersInput
  or: [ComponentSectionsHomepageMayorAndCouncilFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsHomepageMayorAndCouncilInput {
  councilCard: ComponentBlocksCommonLinkInput
  id: ID
  mayorCard: ComponentBlocksCommonLinkInput
  text: String
  title: String
}

type ComponentSectionsHomepageTabs {
  id: ID!
  leftArticle: Article
  newsPageLink: ComponentBlocksCommonLink
  officialBoardPageLink: ComponentBlocksCommonLink
  rightArticle: Article
  roadClosuresPageLink: ComponentBlocksCommonLink
}

input ComponentSectionsHomepageTabsFiltersInput {
  and: [ComponentSectionsHomepageTabsFiltersInput]
  leftArticle: ArticleFiltersInput
  newsPageLink: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsHomepageTabsFiltersInput
  officialBoardPageLink: ComponentBlocksCommonLinkFiltersInput
  or: [ComponentSectionsHomepageTabsFiltersInput]
  rightArticle: ArticleFiltersInput
  roadClosuresPageLink: ComponentBlocksCommonLinkFiltersInput
}

input ComponentSectionsHomepageTabsInput {
  id: ID
  leftArticle: ID
  newsPageLink: ComponentBlocksCommonLinkInput
  officialBoardPageLink: ComponentBlocksCommonLinkInput
  rightArticle: ID
  roadClosuresPageLink: ComponentBlocksCommonLinkInput
}

type ComponentSectionsIframe {
  allowFullscreen: Boolean!
  allowGeolocation: Boolean
  css: String
  fullHeight: Boolean!
  hasBorder: Boolean
  id: ID!
  iframeHeight: String!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSIFRAME_TITLELEVEL
  url: String!
}

input ComponentSectionsIframeFiltersInput {
  allowFullscreen: BooleanFilterInput
  allowGeolocation: BooleanFilterInput
  and: [ComponentSectionsIframeFiltersInput]
  css: StringFilterInput
  fullHeight: BooleanFilterInput
  hasBorder: BooleanFilterInput
  iframeHeight: StringFilterInput
  not: ComponentSectionsIframeFiltersInput
  or: [ComponentSectionsIframeFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
  url: StringFilterInput
}

input ComponentSectionsIframeInput {
  allowFullscreen: Boolean
  allowGeolocation: Boolean
  css: String
  fullHeight: Boolean
  hasBorder: Boolean
  id: ID
  iframeHeight: String
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSIFRAME_TITLELEVEL
  url: String
}

type ComponentSectionsInbaLatestRelease {
  allReleasesPage: Page
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  id: ID!
}

input ComponentSectionsInbaLatestReleaseFiltersInput {
  allReleasesPage: PageFiltersInput
  and: [ComponentSectionsInbaLatestReleaseFiltersInput]
  articles: ArticleFiltersInput
  not: ComponentSectionsInbaLatestReleaseFiltersInput
  or: [ComponentSectionsInbaLatestReleaseFiltersInput]
}

input ComponentSectionsInbaLatestReleaseInput {
  allReleasesPage: ID
  articles: [ID]
  id: ID
}

type ComponentSectionsInbaReleases {
  id: ID!
  showMoreLink: ComponentBlocksCommonLink
  text: String
  title: String
  variant: ENUM_COMPONENTSECTIONSINBARELEASES_VARIANT
}

input ComponentSectionsInbaReleasesFiltersInput {
  and: [ComponentSectionsInbaReleasesFiltersInput]
  not: ComponentSectionsInbaReleasesFiltersInput
  or: [ComponentSectionsInbaReleasesFiltersInput]
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  text: StringFilterInput
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentSectionsInbaReleasesInput {
  id: ID
  showMoreLink: ComponentBlocksCommonLinkInput
  text: String
  title: String
  variant: ENUM_COMPONENTSECTIONSINBARELEASES_VARIANT
}

type ComponentSectionsLinks {
  id: ID!
  pageLinks(filters: ComponentBlocksPageLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksPageLink]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSLINKS_TITLELEVEL
}

input ComponentSectionsLinksFiltersInput {
  and: [ComponentSectionsLinksFiltersInput]
  not: ComponentSectionsLinksFiltersInput
  or: [ComponentSectionsLinksFiltersInput]
  pageLinks: ComponentBlocksPageLinkFiltersInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsLinksInput {
  id: ID
  pageLinks: [ComponentBlocksPageLinkInput]
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSLINKS_TITLELEVEL
}

type ComponentSectionsNarrowText {
  content: String
  id: ID!
  width: ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH
}

input ComponentSectionsNarrowTextFiltersInput {
  and: [ComponentSectionsNarrowTextFiltersInput]
  content: StringFilterInput
  not: ComponentSectionsNarrowTextFiltersInput
  or: [ComponentSectionsNarrowTextFiltersInput]
  width: StringFilterInput
}

input ComponentSectionsNarrowTextInput {
  content: String
  id: ID
  width: ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH
}

type ComponentSectionsNewsletter {
  facebookUrl: String
  id: ID!
  instagramUrl: String
  newsletterType: ENUM_COMPONENTSECTIONSNEWSLETTER_NEWSLETTERTYPE!
  socialLinksTitle: String
  text: String
  title: String
}

input ComponentSectionsNewsletterFiltersInput {
  and: [ComponentSectionsNewsletterFiltersInput]
  facebookUrl: StringFilterInput
  instagramUrl: StringFilterInput
  newsletterType: StringFilterInput
  not: ComponentSectionsNewsletterFiltersInput
  or: [ComponentSectionsNewsletterFiltersInput]
  socialLinksTitle: StringFilterInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsNewsletterInput {
  facebookUrl: String
  id: ID
  instagramUrl: String
  newsletterType: ENUM_COMPONENTSECTIONSNEWSLETTER_NEWSLETTERTYPE
  socialLinksTitle: String
  text: String
  title: String
}

type ComponentSectionsNumbersOverview {
  id: ID!
  numbersOverviewItems(filters: ComponentBlocksNumbersOverviewItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksNumbersOverviewItem]
  showMoreLink: ComponentBlocksCommonLink
  text: String
  title: String
}

input ComponentSectionsNumbersOverviewFiltersInput {
  and: [ComponentSectionsNumbersOverviewFiltersInput]
  not: ComponentSectionsNumbersOverviewFiltersInput
  numbersOverviewItems: ComponentBlocksNumbersOverviewItemFiltersInput
  or: [ComponentSectionsNumbersOverviewFiltersInput]
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsNumbersOverviewInput {
  id: ID
  numbersOverviewItems: [ComponentBlocksNumbersOverviewItemInput]
  showMoreLink: ComponentBlocksCommonLinkInput
  text: String
  title: String
}

type ComponentSectionsNumericalList {
  id: ID!
  items(filters: ComponentBlocksNumericalListItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksNumericalListItem]
  text: String
  title: String
  variant: ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT!
}

input ComponentSectionsNumericalListFiltersInput {
  and: [ComponentSectionsNumericalListFiltersInput]
  items: ComponentBlocksNumericalListItemFiltersInput
  not: ComponentSectionsNumericalListFiltersInput
  or: [ComponentSectionsNumericalListFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  variant: StringFilterInput
}

input ComponentSectionsNumericalListInput {
  id: ID
  items: [ComponentBlocksNumericalListItemInput]
  text: String
  title: String
  variant: ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT
}

type ComponentSectionsOfficialBoard {
  id: ID!
}

input ComponentSectionsOfficialBoardFiltersInput {
  and: [ComponentSectionsOfficialBoardFiltersInput]
  not: ComponentSectionsOfficialBoardFiltersInput
  or: [ComponentSectionsOfficialBoardFiltersInput]
}

input ComponentSectionsOfficialBoardInput {
  id: ID
}

type ComponentSectionsOpeningHours {
  alertMessage: ComponentBlocksOpeningHoursAlertMessage
  id: ID!
  openingHoursItems(filters: ComponentBlocksOpeningHoursItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksOpeningHoursItem]
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSOPENINGHOURS_TITLELEVEL
}

input ComponentSectionsOpeningHoursFiltersInput {
  alertMessage: ComponentBlocksOpeningHoursAlertMessageFiltersInput
  and: [ComponentSectionsOpeningHoursFiltersInput]
  not: ComponentSectionsOpeningHoursFiltersInput
  openingHoursItems: ComponentBlocksOpeningHoursItemFiltersInput
  or: [ComponentSectionsOpeningHoursFiltersInput]
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsOpeningHoursInput {
  alertMessage: ComponentBlocksOpeningHoursAlertMessageInput
  id: ID
  openingHoursItems: [ComponentBlocksOpeningHoursItemInput]
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSOPENINGHOURS_TITLELEVEL
}

type ComponentSectionsOrganizationalStructure {
  id: ID!
  title: String
}

input ComponentSectionsOrganizationalStructureFiltersInput {
  and: [ComponentSectionsOrganizationalStructureFiltersInput]
  not: ComponentSectionsOrganizationalStructureFiltersInput
  or: [ComponentSectionsOrganizationalStructureFiltersInput]
  title: StringFilterInput
}

input ComponentSectionsOrganizationalStructureInput {
  id: ID
  title: String
}

type ComponentSectionsPartners {
  id: ID!
  logoRatio: ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO!
  partners(filters: ComponentBlocksPartnerFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksPartner]!
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSPARTNERS_TITLELEVEL
}

input ComponentSectionsPartnersFiltersInput {
  and: [ComponentSectionsPartnersFiltersInput]
  logoRatio: StringFilterInput
  not: ComponentSectionsPartnersFiltersInput
  or: [ComponentSectionsPartnersFiltersInput]
  partners: ComponentBlocksPartnerFiltersInput
  text: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
}

input ComponentSectionsPartnersInput {
  id: ID
  logoRatio: ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO
  partners: [ComponentBlocksPartnerInput]
  text: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSPARTNERS_TITLELEVEL
}

type ComponentSectionsProsAndConsSection {
  cons: ComponentBlocksProsAndConsCard!
  id: ID!
  pros: ComponentBlocksProsAndConsCard!
  text: String
  textAlign: ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN!
  title: String
}

input ComponentSectionsProsAndConsSectionFiltersInput {
  and: [ComponentSectionsProsAndConsSectionFiltersInput]
  cons: ComponentBlocksProsAndConsCardFiltersInput
  not: ComponentSectionsProsAndConsSectionFiltersInput
  or: [ComponentSectionsProsAndConsSectionFiltersInput]
  pros: ComponentBlocksProsAndConsCardFiltersInput
  text: StringFilterInput
  textAlign: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsProsAndConsSectionInput {
  cons: ComponentBlocksProsAndConsCardInput
  id: ID
  pros: ComponentBlocksProsAndConsCardInput
  text: String
  textAlign: ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN
  title: String
}

type ComponentSectionsRegulations {
  id: ID!
  regulations(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Regulation]!
  regulations_connection(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RegulationRelationResponseCollection
  showAll: Boolean
}

input ComponentSectionsRegulationsFiltersInput {
  and: [ComponentSectionsRegulationsFiltersInput]
  not: ComponentSectionsRegulationsFiltersInput
  or: [ComponentSectionsRegulationsFiltersInput]
  regulations: RegulationFiltersInput
  showAll: BooleanFilterInput
}

input ComponentSectionsRegulationsInput {
  id: ID
  regulations: [ID]
  showAll: Boolean
}

type ComponentSectionsStarzLandingPage {
  banner: ComponentBlocksStarzLandingPageBanner!
  cardLinks(filters: ComponentBlocksCardLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCardLink]
  id: ID!
}

input ComponentSectionsStarzLandingPageFiltersInput {
  and: [ComponentSectionsStarzLandingPageFiltersInput]
  banner: ComponentBlocksStarzLandingPageBannerFiltersInput
  cardLinks: ComponentBlocksCardLinkFiltersInput
  not: ComponentSectionsStarzLandingPageFiltersInput
  or: [ComponentSectionsStarzLandingPageFiltersInput]
}

input ComponentSectionsStarzLandingPageInput {
  banner: ComponentBlocksStarzLandingPageBannerInput
  cardLinks: [ComponentBlocksCardLinkInput]
  id: ID
}

type ComponentSectionsSubnavigation {
  id: ID!
  links(filters: ComponentBlocksSubnavigationLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksSubnavigationLink]
}

input ComponentSectionsSubnavigationFiltersInput {
  and: [ComponentSectionsSubnavigationFiltersInput]
  links: ComponentBlocksSubnavigationLinkFiltersInput
  not: ComponentSectionsSubnavigationFiltersInput
  or: [ComponentSectionsSubnavigationFiltersInput]
}

input ComponentSectionsSubnavigationInput {
  id: ID
  links: [ComponentBlocksSubnavigationLinkInput]
}

type ComponentSectionsTextWithImage {
  content: String
  id: ID!
  imageAspectRatio: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION!
  imageSrc: UploadFile!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
}

input ComponentSectionsTextWithImageFiltersInput {
  and: [ComponentSectionsTextWithImageFiltersInput]
  content: StringFilterInput
  imageAspectRatio: StringFilterInput
  imagePosition: StringFilterInput
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsTextWithImageFiltersInput
  or: [ComponentSectionsTextWithImageFiltersInput]
}

input ComponentSectionsTextWithImageInput {
  content: String
  id: ID
  imageAspectRatio: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION
  imageSrc: ID
  links: [ComponentBlocksCommonLinkInput]
}

type ComponentSectionsTextWithImageOverlapped {
  content: String
  id: ID!
  image: UploadFile!
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION!
  links(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
}

input ComponentSectionsTextWithImageOverlappedFiltersInput {
  and: [ComponentSectionsTextWithImageOverlappedFiltersInput]
  content: StringFilterInput
  imagePosition: StringFilterInput
  links: ComponentBlocksCommonLinkFiltersInput
  not: ComponentSectionsTextWithImageOverlappedFiltersInput
  or: [ComponentSectionsTextWithImageOverlappedFiltersInput]
}

input ComponentSectionsTextWithImageOverlappedInput {
  content: String
  id: ID
  image: ID
  imagePosition: ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION
  links: [ComponentBlocksCommonLinkInput]
}

type ComponentSectionsTootootEvents {
  id: ID!
  showMoreLink: ComponentBlocksCommonLink
  text: String
  title: String
}

input ComponentSectionsTootootEventsFiltersInput {
  and: [ComponentSectionsTootootEventsFiltersInput]
  not: ComponentSectionsTootootEventsFiltersInput
  or: [ComponentSectionsTootootEventsFiltersInput]
  showMoreLink: ComponentBlocksCommonLinkFiltersInput
  text: StringFilterInput
  title: StringFilterInput
}

input ComponentSectionsTootootEventsInput {
  id: ID
  showMoreLink: ComponentBlocksCommonLinkInput
  text: String
  title: String
}

type ComponentSectionsTopServices {
  id: ID!
  services(filters: ComponentBlocksTopServicesItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksTopServicesItem]!
  title: String!
}

input ComponentSectionsTopServicesFiltersInput {
  and: [ComponentSectionsTopServicesFiltersInput]
  not: ComponentSectionsTopServicesFiltersInput
  or: [ComponentSectionsTopServicesFiltersInput]
  services: ComponentBlocksTopServicesItemFiltersInput
  title: StringFilterInput
}

input ComponentSectionsTopServicesInput {
  id: ID
  services: [ComponentBlocksTopServicesItemInput]
  title: String
}

type ComponentSectionsVideos {
  id: ID!
  subtitle: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSVIDEOS_TITLELEVEL
  videos(filters: ComponentBlocksVideoFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksVideo]
}

input ComponentSectionsVideosFiltersInput {
  and: [ComponentSectionsVideosFiltersInput]
  not: ComponentSectionsVideosFiltersInput
  or: [ComponentSectionsVideosFiltersInput]
  subtitle: StringFilterInput
  title: StringFilterInput
  titleLevel: StringFilterInput
  videos: ComponentBlocksVideoFiltersInput
}

input ComponentSectionsVideosInput {
  id: ID
  subtitle: String
  title: String
  titleLevel: ENUM_COMPONENTSECTIONSVIDEOS_TITLELEVEL
  videos: [ComponentBlocksVideoInput]
}

type ComponentSidebarsEmptySidebar {
  id: ID!
}

input ComponentSidebarsEmptySidebarFiltersInput {
  and: [ComponentSidebarsEmptySidebarFiltersInput]
  not: ComponentSidebarsEmptySidebarFiltersInput
  or: [ComponentSidebarsEmptySidebarFiltersInput]
}

input ComponentSidebarsEmptySidebarInput {
  id: ID
}

type ComponentTaxAdministratorsTaxAdministrator {
  email: String!
  id: ID!
  name: String!
  officeNumber: String!
  phone: String!
  range: String!
}

input ComponentTaxAdministratorsTaxAdministratorFiltersInput {
  and: [ComponentTaxAdministratorsTaxAdministratorFiltersInput]
  email: StringFilterInput
  name: StringFilterInput
  not: ComponentTaxAdministratorsTaxAdministratorFiltersInput
  officeNumber: StringFilterInput
  or: [ComponentTaxAdministratorsTaxAdministratorFiltersInput]
  phone: StringFilterInput
  range: StringFilterInput
}

input ComponentTaxAdministratorsTaxAdministratorInput {
  email: String
  id: ID
  name: String
  officeNumber: String
  phone: String
  range: String
}

"""
A date string, such as 2007-12-03, compliant with the `full-date` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar Date

input DateFilterInput {
  and: [Date]
  between: [Date]
  contains: Date
  containsi: Date
  endsWith: Date
  eq: Date
  eqi: Date
  gt: Date
  gte: Date
  in: [Date]
  lt: Date
  lte: Date
  ne: Date
  nei: Date
  not: DateFilterInput
  notContains: Date
  notContainsi: Date
  notIn: [Date]
  notNull: Boolean
  null: Boolean
  or: [Date]
  startsWith: Date
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.
"""
scalar DateTime

input DateTimeFilterInput {
  and: [DateTime]
  between: [DateTime]
  contains: DateTime
  containsi: DateTime
  endsWith: DateTime
  eq: DateTime
  eqi: DateTime
  gt: DateTime
  gte: DateTime
  in: [DateTime]
  lt: DateTime
  lte: DateTime
  ne: DateTime
  nei: DateTime
  not: DateTimeFilterInput
  notContains: DateTime
  notContainsi: DateTime
  notIn: [DateTime]
  notNull: Boolean
  null: Boolean
  or: [DateTime]
  startsWith: DateTime
}

type DeleteMutationResponse {
  documentId: ID!
}

type Document {
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  createdAt: DateTime
  description: String
  documentCategory: DocumentCategory
  documentId: ID!
  files(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UploadFile]!
  files_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection!
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type DocumentCategory {
  createdAt: DateTime
  documentId: ID!
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Document]!
  documents_connection(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): DocumentRelationResponseCollection
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type DocumentCategoryEntity {
  attributes: DocumentCategory
  id: ID
}

type DocumentCategoryEntityResponse {
  data: DocumentCategory
}

type DocumentCategoryEntityResponseCollection {
  nodes: [DocumentCategory!]!
  pageInfo: Pagination!
}

input DocumentCategoryFiltersInput {
  and: [DocumentCategoryFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  documents: DocumentFiltersInput
  not: DocumentCategoryFiltersInput
  or: [DocumentCategoryFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input DocumentCategoryInput {
  documents: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type DocumentCategoryRelationResponseCollection {
  nodes: [DocumentCategory!]!
}

type DocumentEntity {
  attributes: Document
  id: ID
}

type DocumentEntityResponse {
  data: Document
}

type DocumentEntityResponseCollection {
  nodes: [Document!]!
  pageInfo: Pagination!
}

input DocumentFiltersInput {
  adminGroups: AdminGroupFiltersInput
  and: [DocumentFiltersInput]
  createdAt: DateTimeFilterInput
  description: StringFilterInput
  documentCategory: DocumentCategoryFiltersInput
  documentId: IDFilterInput
  not: DocumentFiltersInput
  or: [DocumentFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input DocumentInput {
  adminGroups: [ID]
  description: String
  documentCategory: ID
  files: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type DocumentRelationResponseCollection {
  nodes: [Document!]!
}

enum ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_CONTENTPOSITION {
  left
  right
}

enum ENUM_COMPONENTBLOCKSSTARZLANDINGPAGEBANNER_VARIANT {
  color
  dark
  white_condensed
}

enum ENUM_COMPONENTBLOCKSTOPSERVICESITEM_ICON {
  bratislavske_konto
  dane_a_poplatky
  kampane_a_projekty
  nahlasenie_podnetov
  organizacna_struktura
  pracovne_prilezitosti
  prenajom_priestorov
  turistom_v_hlavnom_meste
  uradne_hodiny
  verejne_priestory
}

enum ENUM_COMPONENTGENERALHEADERLINK_ICON {
  esluzby
  kontakt
  som_turista
  ukraina
}

enum ENUM_COMPONENTMENUMENUITEM_ICON {
  doprava_mapy_02
  kultura_06
  mesto_01
  socialna_pomoc_04
  vzdelavanie_05
  zp_vystavba_03
}

enum ENUM_COMPONENTMENUMENUSECTION_ICON {
  aktivity_04
  byvanie_04
  covid_06
  cyklo_02
  dane_01
  dedicstvo_06
  deti_a_mladez_05
  doprava_02
  dotacie_05
  kalendar_06
  klima_03
  komunity_06
  koncepcia_06
  mapy_02
  mhd_02
  ocenovanie_05
  organizacie_06
  parkovanie_02
  partnerstva_01
  pomoc_04
  projekty_01
  rozvoj_mesta_03
  skolstvo_05
  sluzby_04
  sluzby_06
  sport_05
  sprava_a_udrzba_02
  sprava_mesta_01
  transparentne_mesto_01
  uzemny_plan_03
  verejne_osvetlenie_03
  vystavba_a_nehnutelnosti_03
  zariadenia_04
  zdielana_mobilita_02
  zelen_03
  zivotne_prostredie_03
}

enum ENUM_COMPONENTSECTIONSACCORDION_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSBANNER_CONTENTPOSITION {
  left
  right
}

enum ENUM_COMPONENTSECTIONSBANNER_VARIANT {
  color
  dark
  white_condensed
}

enum ENUM_COMPONENTSECTIONSCOLUMNS_IMAGEVARIANT {
  columnsSection_imageVariant_imageOriginalSize
  columnsSection_imageVariant_withCircleBackground
}

enum ENUM_COMPONENTSECTIONSCOLUMNS_RESPONSIVELAYOUT {
  columnsSection_responsiveLayout_oneColumn
  columnsSection_responsiveLayout_slider
}

enum ENUM_COMPONENTSECTIONSCOMPARISONSECTION_TEXTALIGN {
  center
  left
}

enum ENUM_COMPONENTSECTIONSCONTACTSSECTION_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSDIVIDER_STYLE {
  bicykel_02_full_width
  budovy_04_full_width
  byvanie_04_full_width
  divadlo
  doprava_02_full_width
  hrad_01_full_width
  lod_02_full_width
  mesto_01_full_width
  park_04_full_width
  parkovanie_02_full_width
  skola
  stromy_03_full_width
  vystavba_03_full_width
  vzdelavanie
}

enum ENUM_COMPONENTSECTIONSDOCUMENTS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSEVENTS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSFACILITIES_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSFAQS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSFILELIST_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSGALLERY_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSIFRAME_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSINBARELEASES_VARIANT {
  carousel
  grid
}

enum ENUM_COMPONENTSECTIONSLINKS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSNARROWTEXT_WIDTH {
  default
  full
  narrow
  wide
}

enum ENUM_COMPONENTSECTIONSNEWSLETTER_NEWSLETTERTYPE {
  starz
}

enum ENUM_COMPONENTSECTIONSNUMERICALLIST_VARIANT {
  basic
  roadmap
}

enum ENUM_COMPONENTSECTIONSOPENINGHOURS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSPARTNERS_LOGORATIO {
  ratio_4_1
  ratio_4_3
}

enum ENUM_COMPONENTSECTIONSPARTNERS_TITLELEVEL {
  h2
  h3
}

enum ENUM_COMPONENTSECTIONSPROSANDCONSSECTION_TEXTALIGN {
  center
  left
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGEOVERLAPPED_IMAGEPOSITION {
  left
  left_shifted
  right
  right_shifted
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEASPECTRATIO {
  ratio_1_1
  ratio_4_3
}

enum ENUM_COMPONENTSECTIONSTEXTWITHIMAGE_IMAGEPOSITION {
  left
  right
}

enum ENUM_COMPONENTSECTIONSVIDEOS_TITLELEVEL {
  h2
  h3
}

enum ENUM_INTERNALJOB_JOBTYPE {
  CREATE_REDIRECT
  RECALCULATE_FULLPATH
}

enum ENUM_INTERNALJOB_STATE {
  completed
  failed
  pending
}

enum ENUM_PAGECATEGORY_COLOR {
  blue
  brown
  green
  purple
  red
  yellow
}

enum ENUM_PAGECATEGORY_ICON {
  doprava_mapy_02
  kultura_06
  mesto_01
  socialna_pomoc_04
  vzdelavanie_05
  zp_vystavba_03
}

enum ENUM_PAGE_PAGECOLOR {
  blue
  brown
  green
  purple
  red
  starz
  yellow
}

enum ENUM_REGULATION_CATEGORY {
  archiv
  daneAPoplatky
  hospodarenie
  ostatne
  pomenovanieUlic
  poriadokACistota
  socialnaPomocASkolstvo
  uzemnePlanovanie
}

type Error {
  code: String!
  message: String
}

type Faq {
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  body: String
  createdAt: DateTime
  documentId: ID!
  faqCategory: FaqCategory
  locale: String
  localizations(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Faq]!
  localizations_connection(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqRelationResponseCollection
  publishedAt: DateTime
  title: String!
  updatedAt: DateTime
}

type FaqCategory {
  createdAt: DateTime
  documentId: ID!
  faqs(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Faq]!
  faqs_connection(filters: FaqFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqRelationResponseCollection
  locale: String
  localizations(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [FaqCategory]!
  localizations_connection(filters: FaqCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): FaqCategoryRelationResponseCollection
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type FaqCategoryEntity {
  attributes: FaqCategory
  id: ID
}

type FaqCategoryEntityResponse {
  data: FaqCategory
}

type FaqCategoryEntityResponseCollection {
  nodes: [FaqCategory!]!
  pageInfo: Pagination!
}

input FaqCategoryFiltersInput {
  and: [FaqCategoryFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  faqs: FaqFiltersInput
  locale: StringFilterInput
  localizations: FaqCategoryFiltersInput
  not: FaqCategoryFiltersInput
  or: [FaqCategoryFiltersInput]
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input FaqCategoryInput {
  faqs: [ID]
  publishedAt: DateTime
  slug: String
  title: String
}

type FaqCategoryRelationResponseCollection {
  nodes: [FaqCategory!]!
}

type FaqEntity {
  attributes: Faq
  id: ID
}

type FaqEntityResponse {
  data: Faq
}

type FaqEntityResponseCollection {
  nodes: [Faq!]!
  pageInfo: Pagination!
}

input FaqFiltersInput {
  adminGroups: AdminGroupFiltersInput
  and: [FaqFiltersInput]
  body: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  faqCategory: FaqCategoryFiltersInput
  locale: StringFilterInput
  localizations: FaqFiltersInput
  not: FaqFiltersInput
  or: [FaqFiltersInput]
  publishedAt: DateTimeFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input FaqInput {
  adminGroups: [ID]
  body: String
  faqCategory: ID
  publishedAt: DateTime
  title: String
}

type FaqRelationResponseCollection {
  nodes: [Faq!]!
}

input FileInfoInput {
  alternativeText: String
  caption: String
  name: String
}

input FloatFilterInput {
  and: [Float]
  between: [Float]
  contains: Float
  containsi: Float
  endsWith: Float
  eq: Float
  eqi: Float
  gt: Float
  gte: Float
  in: [Float]
  lt: Float
  lte: Float
  ne: Float
  nei: Float
  not: FloatFilterInput
  notContains: Float
  notContainsi: Float
  notIn: [Float]
  notNull: Boolean
  null: Boolean
  or: [Float]
  startsWith: Float
}

type Footer {
  accessibilityPageLink: ComponentBlocksCommonLink
  columns(filters: ComponentBlocksFooterColumnFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFooterColumn]
  contactText: String
  createdAt: DateTime
  documentId: ID!
  facebookUrl: String
  innovationsLink: ComponentBlocksCommonLink
  instagramUrl: String
  linkedinUrl: String
  locale: String
  localizations: [Footer]!
  localizations_connection: FooterRelationResponseCollection
  publishedAt: DateTime
  tiktokUrl: String
  updatedAt: DateTime
  youtubeUrl: String
}

type FooterEntity {
  attributes: Footer
  id: ID
}

type FooterEntityResponse {
  data: Footer
}

type FooterEntityResponseCollection {
  nodes: [Footer!]!
  pageInfo: Pagination!
}

input FooterFiltersInput {
  accessibilityPageLink: ComponentBlocksCommonLinkFiltersInput
  and: [FooterFiltersInput]
  columns: ComponentBlocksFooterColumnFiltersInput
  contactText: StringFilterInput
  createdAt: DateTimeFilterInput
  facebookUrl: StringFilterInput
  innovationsLink: ComponentBlocksCommonLinkFiltersInput
  instagramUrl: StringFilterInput
  linkedinUrl: StringFilterInput
  locale: StringFilterInput
  localizations: FooterFiltersInput
  not: FooterFiltersInput
  or: [FooterFiltersInput]
  publishedAt: DateTimeFilterInput
  tiktokUrl: StringFilterInput
  updatedAt: DateTimeFilterInput
  youtubeUrl: StringFilterInput
}

input FooterInput {
  accessibilityPageLink: ComponentBlocksCommonLinkInput
  columns: [ComponentBlocksFooterColumnInput]
  contactText: String
  facebookUrl: String
  innovationsLink: ComponentBlocksCommonLinkInput
  instagramUrl: String
  linkedinUrl: String
  publishedAt: DateTime
  tiktokUrl: String
  youtubeUrl: String
}

type FooterRelationResponseCollection {
  nodes: [Footer!]!
}

type General {
  createdAt: DateTime
  documentId: ID!
  documentsPage: Page
  header: ComponentGeneralHeader
  inbaPage: Page
  inbaReleasesPage: Page
  locale: String
  localizations: [General]!
  localizations_connection: GeneralRelationResponseCollection
  newsPage: Page
  officialBoardPage: Page
  privacyPolicyPage: Page
  publishedAt: DateTime
  updatedAt: DateTime
  vznPage: Page
}

type GeneralEntity {
  attributes: General
  id: ID
}

type GeneralEntityResponse {
  data: General
}

type GeneralEntityResponseCollection {
  nodes: [General!]!
  pageInfo: Pagination!
}

input GeneralFiltersInput {
  and: [GeneralFiltersInput]
  createdAt: DateTimeFilterInput
  documentsPage: PageFiltersInput
  header: ComponentGeneralHeaderFiltersInput
  inbaPage: PageFiltersInput
  inbaReleasesPage: PageFiltersInput
  locale: StringFilterInput
  localizations: GeneralFiltersInput
  newsPage: PageFiltersInput
  not: GeneralFiltersInput
  officialBoardPage: PageFiltersInput
  or: [GeneralFiltersInput]
  privacyPolicyPage: PageFiltersInput
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
  vznPage: PageFiltersInput
}

input GeneralInput {
  documentsPage: ID
  header: ComponentGeneralHeaderInput
  inbaPage: ID
  inbaReleasesPage: ID
  newsPage: ID
  officialBoardPage: ID
  privacyPolicyPage: ID
  publishedAt: DateTime
  vznPage: ID
}

type GeneralRelationResponseCollection {
  nodes: [General!]!
}

union GenericMorph = AdminGroup | Alert | Article | ArticleCategory | ComponentAccordionItemsFlatText | ComponentAccordionItemsInstitution | ComponentAccordionItemsInstitutionNarrow | ComponentBlocksCardLink | ComponentBlocksColumnsItem | ComponentBlocksCommonLink | ComponentBlocksComparisonCard | ComponentBlocksComparisonItem | ComponentBlocksContactCard | ComponentBlocksContactDirectionsCard | ComponentBlocksContactPersonCard | ComponentBlocksFile | ComponentBlocksFileItem | ComponentBlocksFooterColumn | ComponentBlocksHomepageHighlightsItem | ComponentBlocksInBa | ComponentBlocksNumbersOverviewItem | ComponentBlocksNumericalListItem | ComponentBlocksOpeningHoursAlertMessage | ComponentBlocksOpeningHoursItem | ComponentBlocksPageLink | ComponentBlocksPartner | ComponentBlocksProsAndConsCard | ComponentBlocksStarzLandingPageBanner | ComponentBlocksSubnavigationLink | ComponentBlocksTopServicesItem | ComponentBlocksVideo | ComponentGeneralHeader | ComponentGeneralHeaderLink | ComponentHeaderSectionsEvent | ComponentHeaderSectionsFacility | ComponentMenuMenuItem | ComponentMenuMenuLink | ComponentMenuMenuSection | ComponentSectionsAccordion | ComponentSectionsArticles | ComponentSectionsBanner | ComponentSectionsCalculator | ComponentSectionsColumnedText | ComponentSectionsColumns | ComponentSectionsComparisonSection | ComponentSectionsContactsSection | ComponentSectionsDivider | ComponentSectionsDocuments | ComponentSectionsEvents | ComponentSectionsFacilities | ComponentSectionsFaqCategories | ComponentSectionsFaqs | ComponentSectionsFileList | ComponentSectionsGallery | ComponentSectionsHomepageEvents | ComponentSectionsHomepageHighlights | ComponentSectionsHomepageMayorAndCouncil | ComponentSectionsHomepageTabs | ComponentSectionsIframe | ComponentSectionsInbaLatestRelease | ComponentSectionsInbaReleases | ComponentSectionsLinks | ComponentSectionsNarrowText | ComponentSectionsNewsletter | ComponentSectionsNumbersOverview | ComponentSectionsNumericalList | ComponentSectionsOfficialBoard | ComponentSectionsOpeningHours | ComponentSectionsOrganizationalStructure | ComponentSectionsPartners | ComponentSectionsProsAndConsSection | ComponentSectionsRegulations | ComponentSectionsStarzLandingPage | ComponentSectionsSubnavigation | ComponentSectionsTextWithImage | ComponentSectionsTextWithImageOverlapped | ComponentSectionsTootootEvents | ComponentSectionsTopServices | ComponentSectionsVideos | ComponentSidebarsEmptySidebar | ComponentTaxAdministratorsTaxAdministrator | Document | DocumentCategory | Faq | FaqCategory | Footer | General | Homepage | I18NLocale | InbaArticle | InbaRelease | InbaTag | InternalJob | Menu | Page | PageCategory | Redirect | Regulation | ReviewWorkflowsWorkflow | ReviewWorkflowsWorkflowStage | Tag | TaxAdministratorsList | UploadFile | UsersPermissionsPermission | UsersPermissionsRole | UsersPermissionsUser

type Homepage {
  createdAt: DateTime
  documentId: ID!
  eventsSection: ComponentSectionsTootootEvents
  highlights: ComponentSectionsHomepageHighlights
  inba: ComponentBlocksInBa
  locale: String
  localizations: [Homepage]!
  localizations_connection: HomepageRelationResponseCollection
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncil
  metaDescription: String!
  metaTitle: String!
  publishedAt: DateTime
  tabs: ComponentSectionsHomepageTabs
  topServices: ComponentSectionsTopServices
  updatedAt: DateTime
  welcomeHeadline: String!
  welcomeMedia: UploadFile!
}

type HomepageEntity {
  attributes: Homepage
  id: ID
}

type HomepageEntityResponse {
  data: Homepage
}

type HomepageEntityResponseCollection {
  nodes: [Homepage!]!
  pageInfo: Pagination!
}

input HomepageFiltersInput {
  and: [HomepageFiltersInput]
  createdAt: DateTimeFilterInput
  eventsSection: ComponentSectionsTootootEventsFiltersInput
  highlights: ComponentSectionsHomepageHighlightsFiltersInput
  inba: ComponentBlocksInBaFiltersInput
  locale: StringFilterInput
  localizations: HomepageFiltersInput
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncilFiltersInput
  metaDescription: StringFilterInput
  metaTitle: StringFilterInput
  not: HomepageFiltersInput
  or: [HomepageFiltersInput]
  publishedAt: DateTimeFilterInput
  tabs: ComponentSectionsHomepageTabsFiltersInput
  topServices: ComponentSectionsTopServicesFiltersInput
  updatedAt: DateTimeFilterInput
  welcomeHeadline: StringFilterInput
}

input HomepageInput {
  eventsSection: ComponentSectionsTootootEventsInput
  highlights: ComponentSectionsHomepageHighlightsInput
  inba: ComponentBlocksInBaInput
  mayorAndCouncil: ComponentSectionsHomepageMayorAndCouncilInput
  metaDescription: String
  metaTitle: String
  publishedAt: DateTime
  tabs: ComponentSectionsHomepageTabsInput
  topServices: ComponentSectionsTopServicesInput
  welcomeHeadline: String
  welcomeMedia: ID
}

type HomepageRelationResponseCollection {
  nodes: [Homepage!]!
}

type I18NLocale {
  code: String
  createdAt: DateTime
  documentId: ID!
  name: String
  publishedAt: DateTime
  updatedAt: DateTime
}

"""A string used to identify an i18n locale"""
scalar I18NLocaleCode

type I18NLocaleEntity {
  attributes: I18NLocale
  id: ID
}

type I18NLocaleEntityResponse {
  data: I18NLocale
}

type I18NLocaleEntityResponseCollection {
  nodes: [I18NLocale!]!
  pageInfo: Pagination!
}

input I18NLocaleFiltersInput {
  and: [I18NLocaleFiltersInput]
  code: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: I18NLocaleFiltersInput
  or: [I18NLocaleFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input I18NLocaleInput {
  code: String
  name: String
  publishedAt: DateTime
}

type I18NLocaleRelationResponseCollection {
  nodes: [I18NLocale!]!
}

input IDFilterInput {
  and: [ID]
  between: [ID]
  contains: ID
  containsi: ID
  endsWith: ID
  eq: ID
  eqi: ID
  gt: ID
  gte: ID
  in: [ID]
  lt: ID
  lte: ID
  ne: ID
  nei: ID
  not: IDFilterInput
  notContains: ID
  notContainsi: ID
  notIn: [ID]
  notNull: Boolean
  null: Boolean
  or: [ID]
  startsWith: ID
}

type InbaArticle {
  content: String
  coverImage: UploadFile
  createdAt: DateTime
  documentId: ID!
  inbaRelease: InbaRelease
  inbaTag: InbaTag
  locale: String
  localizations(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [InbaArticle]!
  localizations_connection(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): InbaArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  slug: String!
  tags(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Tag]!
  tags_connection(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  title: String!
  updatedAt: DateTime
}

type InbaArticleEntity {
  attributes: InbaArticle
  id: ID
}

type InbaArticleEntityResponse {
  data: InbaArticle
}

type InbaArticleEntityResponseCollection {
  nodes: [InbaArticle!]!
  pageInfo: Pagination!
}

input InbaArticleFiltersInput {
  and: [InbaArticleFiltersInput]
  content: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  inbaRelease: InbaReleaseFiltersInput
  inbaTag: InbaTagFiltersInput
  locale: StringFilterInput
  localizations: InbaArticleFiltersInput
  not: InbaArticleFiltersInput
  or: [InbaArticleFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  tags: TagFiltersInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaArticleInput {
  content: String
  coverImage: ID
  inbaRelease: ID
  inbaTag: ID
  perex: String
  publishedAt: DateTime
  slug: String
  tags: [ID]
  title: String
}

type InbaArticleRelationResponseCollection {
  nodes: [InbaArticle!]!
}

type InbaRelease {
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  coverImage: UploadFile
  createdAt: DateTime
  documentId: ID!
  files(filters: ComponentBlocksFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksFile]
  inbaArticles(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [InbaArticle]!
  inbaArticles_connection(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): InbaArticleRelationResponseCollection
  perex: String
  publishedAt: DateTime
  rearImage: UploadFile
  releaseDate: Date!
  slug: String!
  title: String!
  updatedAt: DateTime
}

type InbaReleaseEntity {
  attributes: InbaRelease
  id: ID
}

type InbaReleaseEntityResponse {
  data: InbaRelease
}

type InbaReleaseEntityResponseCollection {
  nodes: [InbaRelease!]!
  pageInfo: Pagination!
}

input InbaReleaseFiltersInput {
  and: [InbaReleaseFiltersInput]
  articles: ArticleFiltersInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  files: ComponentBlocksFileFiltersInput
  inbaArticles: InbaArticleFiltersInput
  not: InbaReleaseFiltersInput
  or: [InbaReleaseFiltersInput]
  perex: StringFilterInput
  publishedAt: DateTimeFilterInput
  releaseDate: DateFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaReleaseInput {
  articles: [ID]
  coverImage: ID
  files: [ComponentBlocksFileInput]
  inbaArticles: [ID]
  perex: String
  publishedAt: DateTime
  rearImage: ID
  releaseDate: Date
  slug: String
  title: String
}

type InbaReleaseRelationResponseCollection {
  nodes: [InbaRelease!]!
}

type InbaTag {
  createdAt: DateTime
  documentId: ID!
  inbaArticles(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [InbaArticle]!
  inbaArticles_connection(filters: InbaArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): InbaArticleRelationResponseCollection
  locale: String
  localizations(filters: InbaTagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [InbaTag]!
  localizations_connection(filters: InbaTagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): InbaTagRelationResponseCollection
  publishedAt: DateTime
  title: String!
  updatedAt: DateTime
}

type InbaTagEntity {
  attributes: InbaTag
  id: ID
}

type InbaTagEntityResponse {
  data: InbaTag
}

type InbaTagEntityResponseCollection {
  nodes: [InbaTag!]!
  pageInfo: Pagination!
}

input InbaTagFiltersInput {
  and: [InbaTagFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  inbaArticles: InbaArticleFiltersInput
  locale: StringFilterInput
  localizations: InbaTagFiltersInput
  not: InbaTagFiltersInput
  or: [InbaTagFiltersInput]
  publishedAt: DateTimeFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InbaTagInput {
  inbaArticles: [ID]
  publishedAt: DateTime
  title: String
}

type InbaTagRelationResponseCollection {
  nodes: [InbaTag!]!
}

input IntFilterInput {
  and: [Int]
  between: [Int]
  contains: Int
  containsi: Int
  endsWith: Int
  eq: Int
  eqi: Int
  gt: Int
  gte: Int
  in: [Int]
  lt: Int
  lte: Int
  ne: Int
  nei: Int
  not: IntFilterInput
  notContains: Int
  notContainsi: Int
  notIn: [Int]
  notNull: Boolean
  null: Boolean
  or: [Int]
  startsWith: Int
}

type InternalJob {
  createdAt: DateTime
  documentId: ID!
  error: String
  jobType: ENUM_INTERNALJOB_JOBTYPE!
  payload: JSON!
  publishedAt: DateTime
  relatedDocumentId: String
  state: ENUM_INTERNALJOB_STATE!
  updatedAt: DateTime
}

type InternalJobEntity {
  attributes: InternalJob
  id: ID
}

type InternalJobEntityResponse {
  data: InternalJob
}

type InternalJobEntityResponseCollection {
  nodes: [InternalJob!]!
  pageInfo: Pagination!
}

input InternalJobFiltersInput {
  and: [InternalJobFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  error: StringFilterInput
  jobType: StringFilterInput
  not: InternalJobFiltersInput
  or: [InternalJobFiltersInput]
  payload: JSONFilterInput
  publishedAt: DateTimeFilterInput
  relatedDocumentId: StringFilterInput
  state: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input InternalJobInput {
  error: String
  jobType: ENUM_INTERNALJOB_JOBTYPE
  payload: JSON
  publishedAt: DateTime
  relatedDocumentId: String
  state: ENUM_INTERNALJOB_STATE
}

type InternalJobRelationResponseCollection {
  nodes: [InternalJob!]!
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

input JSONFilterInput {
  and: [JSON]
  between: [JSON]
  contains: JSON
  containsi: JSON
  endsWith: JSON
  eq: JSON
  eqi: JSON
  gt: JSON
  gte: JSON
  in: [JSON]
  lt: JSON
  lte: JSON
  ne: JSON
  nei: JSON
  not: JSONFilterInput
  notContains: JSON
  notContainsi: JSON
  notIn: [JSON]
  notNull: Boolean
  null: Boolean
  or: [JSON]
  startsWith: JSON
}

"""
The `BigInt` scalar type represents non-fractional signed whole numeric values.
"""
scalar Long

input LongFilterInput {
  and: [Long]
  between: [Long]
  contains: Long
  containsi: Long
  endsWith: Long
  eq: Long
  eqi: Long
  gt: Long
  gte: Long
  in: [Long]
  lt: Long
  lte: Long
  ne: Long
  nei: Long
  not: LongFilterInput
  notContains: Long
  notContainsi: Long
  notIn: [Long]
  notNull: Boolean
  null: Boolean
  or: [Long]
  startsWith: Long
}

type Menu {
  createdAt: DateTime
  documentId: ID!
  locale: String
  localizations: [Menu]!
  localizations_connection: MenuRelationResponseCollection
  menus(filters: ComponentMenuMenuItemFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentMenuMenuItem]
  publishedAt: DateTime
  updatedAt: DateTime
}

type MenuEntity {
  attributes: Menu
  id: ID
}

type MenuEntityResponse {
  data: Menu
}

type MenuEntityResponseCollection {
  nodes: [Menu!]!
  pageInfo: Pagination!
}

input MenuFiltersInput {
  and: [MenuFiltersInput]
  createdAt: DateTimeFilterInput
  locale: StringFilterInput
  localizations: MenuFiltersInput
  menus: ComponentMenuMenuItemFiltersInput
  not: MenuFiltersInput
  or: [MenuFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
}

input MenuInput {
  menus: [ComponentMenuMenuItemInput]
  publishedAt: DateTime
}

type MenuRelationResponseCollection {
  nodes: [Menu!]!
}

type Mutation {
  """Change user password. Confirm with the current password."""
  changePassword(currentPassword: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  createAdminGroup(data: AdminGroupInput!, status: PublicationStatus = PUBLISHED): AdminGroup
  createArticle(
    data: ArticleInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Article
  createArticleCategory(
    data: ArticleCategoryInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): ArticleCategory
  createDocument(data: DocumentInput!, status: PublicationStatus = PUBLISHED): Document
  createDocumentCategory(data: DocumentCategoryInput!, status: PublicationStatus = PUBLISHED): DocumentCategory
  createFaq(
    data: FaqInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Faq
  createFaqCategory(
    data: FaqCategoryInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): FaqCategory
  createInbaArticle(
    data: InbaArticleInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaArticle
  createInbaRelease(data: InbaReleaseInput!, status: PublicationStatus = PUBLISHED): InbaRelease
  createInbaTag(
    data: InbaTagInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaTag
  createInternalJob(data: InternalJobInput!, status: PublicationStatus = PUBLISHED): InternalJob
  createPage(
    data: PageInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Page
  createPageCategory(
    data: PageCategoryInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): PageCategory
  createRedirect(data: RedirectInput!, status: PublicationStatus = PUBLISHED): Redirect
  createRegulation(data: RegulationInput!, status: PublicationStatus = PUBLISHED): Regulation
  createReviewWorkflowsWorkflow(data: ReviewWorkflowsWorkflowInput!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  createReviewWorkflowsWorkflowStage(data: ReviewWorkflowsWorkflowStageInput!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  createTag(
    data: TagInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Tag

  """Create a new role"""
  createUsersPermissionsRole(data: UsersPermissionsRoleInput!): UsersPermissionsCreateRolePayload

  """Create a new user"""
  createUsersPermissionsUser(data: UsersPermissionsUserInput!): UsersPermissionsUserEntityResponse!
  deleteAdminGroup(documentId: ID!): DeleteMutationResponse
  deleteAlert(
    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteArticle(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteArticleCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteDocument(documentId: ID!): DeleteMutationResponse
  deleteDocumentCategory(documentId: ID!): DeleteMutationResponse
  deleteFaq(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteFaqCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteFooter(
    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteGeneral(
    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteHomepage(
    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteInbaArticle(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteInbaRelease(documentId: ID!): DeleteMutationResponse
  deleteInbaTag(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteInternalJob(documentId: ID!): DeleteMutationResponse
  deleteMenu(
    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deletePage(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deletePageCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteRedirect(documentId: ID!): DeleteMutationResponse
  deleteRegulation(documentId: ID!): DeleteMutationResponse
  deleteReviewWorkflowsWorkflow(documentId: ID!): DeleteMutationResponse
  deleteReviewWorkflowsWorkflowStage(documentId: ID!): DeleteMutationResponse
  deleteTag(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
  ): DeleteMutationResponse
  deleteTaxAdministratorsList: DeleteMutationResponse
  deleteUploadFile(id: ID!): UploadFile

  """Delete an existing role"""
  deleteUsersPermissionsRole(id: ID!): UsersPermissionsDeleteRolePayload

  """Delete an existing user"""
  deleteUsersPermissionsUser(id: ID!): UsersPermissionsUserEntityResponse!

  """Confirm an email users email address"""
  emailConfirmation(confirmation: String!): UsersPermissionsLoginPayload

  """Request a reset password token"""
  forgotPassword(email: String!): UsersPermissionsPasswordPayload
  login(input: UsersPermissionsLoginInput!): UsersPermissionsLoginPayload!

  """Register a user"""
  register(input: UsersPermissionsRegisterInput!): UsersPermissionsLoginPayload!

  """
  Reset user password. Confirm with a code (resetToken from forgotPassword)
  """
  resetPassword(code: String!, password: String!, passwordConfirmation: String!): UsersPermissionsLoginPayload
  updateAdminGroup(data: AdminGroupInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): AdminGroup
  updateAlert(
    data: AlertInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Alert
  updateArticle(
    data: ArticleInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Article
  updateArticleCategory(
    data: ArticleCategoryInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): ArticleCategory
  updateDocument(data: DocumentInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Document
  updateDocumentCategory(data: DocumentCategoryInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): DocumentCategory
  updateFaq(
    data: FaqInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Faq
  updateFaqCategory(
    data: FaqCategoryInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): FaqCategory
  updateFooter(
    data: FooterInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Footer
  updateGeneral(
    data: GeneralInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): General
  updateHomepage(
    data: HomepageInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Homepage
  updateInbaArticle(
    data: InbaArticleInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaArticle
  updateInbaRelease(data: InbaReleaseInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): InbaRelease
  updateInbaTag(
    data: InbaTagInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaTag
  updateInternalJob(data: InternalJobInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): InternalJob
  updateMenu(
    data: MenuInput!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Menu
  updatePage(
    data: PageInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Page
  updatePageCategory(
    data: PageCategoryInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): PageCategory
  updateRedirect(data: RedirectInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Redirect
  updateRegulation(data: RegulationInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): Regulation
  updateReviewWorkflowsWorkflow(data: ReviewWorkflowsWorkflowInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  updateReviewWorkflowsWorkflowStage(data: ReviewWorkflowsWorkflowStageInput!, documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  updateTag(
    data: TagInput!
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Tag
  updateTaxAdministratorsList(data: TaxAdministratorsListInput!, status: PublicationStatus = PUBLISHED): TaxAdministratorsList
  updateUploadFile(id: ID!, info: FileInfoInput): UploadFile!

  """Update an existing role"""
  updateUsersPermissionsRole(data: UsersPermissionsRoleInput!, id: ID!): UsersPermissionsUpdateRolePayload

  """Update an existing user"""
  updateUsersPermissionsUser(data: UsersPermissionsUserInput!, id: ID!): UsersPermissionsUserEntityResponse!
}

type Page {
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): AdminGroupRelationResponseCollection
  alias: String
  breadcrumbTitle: String
  childPages(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  childPages_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  children(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  children_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  createdAt: DateTime
  documentId: ID!
  fullPath: String
  headerLinks(filters: ComponentBlocksCommonLinkFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentBlocksCommonLink]
  keywords: String
  locale: String
  localizations(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  localizations_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  metaDescription: String
  metaDiscription: String
  pageBackgroundImage: UploadFile
  pageCategory: PageCategory
  pageColor: ENUM_PAGE_PAGECOLOR!
  pageHeaderSections: [PagePageHeaderSectionsDynamicZone]
  parent: Page
  parentPage: Page
  path: String
  publishedAt: DateTime
  redirects(filters: RedirectFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Redirect]!
  redirects_connection(filters: RedirectFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RedirectRelationResponseCollection
  relatedContents(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Tag]!
  relatedContents_connection(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  sections: [PageSectionsDynamicZone]
  showTableOfContents: Boolean
  sidebar: [PageSidebarDynamicZone]
  slug: String
  subnavigation: ComponentSectionsSubnavigation
  subtext: String
  title: String!
  updatedAt: DateTime
}

type PageCategory {
  color: ENUM_PAGECATEGORY_COLOR
  createdAt: DateTime
  documentId: ID!
  icon: ENUM_PAGECATEGORY_ICON
  locale: String
  localizations(filters: PageCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [PageCategory]!
  localizations_connection(filters: PageCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageCategoryRelationResponseCollection
  pages(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Page]!
  pages_connection(filters: PageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): PageRelationResponseCollection
  publishedAt: DateTime
  shortTitle: String
  title: String
  updatedAt: DateTime
}

type PageCategoryEntity {
  attributes: PageCategory
  id: ID
}

type PageCategoryEntityResponse {
  data: PageCategory
}

type PageCategoryEntityResponseCollection {
  nodes: [PageCategory!]!
  pageInfo: Pagination!
}

input PageCategoryFiltersInput {
  and: [PageCategoryFiltersInput]
  color: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  icon: StringFilterInput
  locale: StringFilterInput
  localizations: PageCategoryFiltersInput
  not: PageCategoryFiltersInput
  or: [PageCategoryFiltersInput]
  pages: PageFiltersInput
  publishedAt: DateTimeFilterInput
  shortTitle: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input PageCategoryInput {
  color: ENUM_PAGECATEGORY_COLOR
  icon: ENUM_PAGECATEGORY_ICON
  pages: [ID]
  publishedAt: DateTime
  shortTitle: String
  title: String
}

type PageCategoryRelationResponseCollection {
  nodes: [PageCategory!]!
}

type PageEntity {
  attributes: Page
  id: ID
}

type PageEntityResponse {
  data: Page
}

type PageEntityResponseCollection {
  nodes: [Page!]!
  pageInfo: Pagination!
}

input PageFiltersInput {
  adminGroups: AdminGroupFiltersInput
  alias: StringFilterInput
  and: [PageFiltersInput]
  breadcrumbTitle: StringFilterInput
  childPages: PageFiltersInput
  children: PageFiltersInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  fullPath: StringFilterInput
  headerLinks: ComponentBlocksCommonLinkFiltersInput
  keywords: StringFilterInput
  locale: StringFilterInput
  localizations: PageFiltersInput
  metaDescription: StringFilterInput
  metaDiscription: StringFilterInput
  not: PageFiltersInput
  or: [PageFiltersInput]
  pageCategory: PageCategoryFiltersInput
  pageColor: StringFilterInput
  parent: PageFiltersInput
  parentPage: PageFiltersInput
  path: StringFilterInput
  publishedAt: DateTimeFilterInput
  redirects: RedirectFiltersInput
  relatedContents: TagFiltersInput
  showTableOfContents: BooleanFilterInput
  slug: StringFilterInput
  subnavigation: ComponentSectionsSubnavigationFiltersInput
  subtext: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input PageInput {
  adminGroups: [ID]
  alias: String
  breadcrumbTitle: String
  childPages: [ID]
  children: [ID]
  fullPath: String
  headerLinks: [ComponentBlocksCommonLinkInput]
  keywords: String
  metaDescription: String
  metaDiscription: String
  pageBackgroundImage: ID
  pageCategory: ID
  pageColor: ENUM_PAGE_PAGECOLOR
  pageHeaderSections: [PagePageHeaderSectionsDynamicZoneInput!]
  parent: ID
  parentPage: ID
  path: String
  publishedAt: DateTime
  redirects: [ID]
  relatedContents: [ID]
  sections: [PageSectionsDynamicZoneInput!]
  showTableOfContents: Boolean
  sidebar: [PageSidebarDynamicZoneInput!]
  slug: String
  subnavigation: ComponentSectionsSubnavigationInput
  subtext: String
  title: String
}

union PagePageHeaderSectionsDynamicZone = ComponentHeaderSectionsEvent | ComponentHeaderSectionsFacility | Error

scalar PagePageHeaderSectionsDynamicZoneInput

type PageRelationResponseCollection {
  nodes: [Page!]!
}

union PageSectionsDynamicZone = ComponentSectionsAccordion | ComponentSectionsArticles | ComponentSectionsBanner | ComponentSectionsCalculator | ComponentSectionsColumnedText | ComponentSectionsColumns | ComponentSectionsComparisonSection | ComponentSectionsContactsSection | ComponentSectionsDivider | ComponentSectionsDocuments | ComponentSectionsEvents | ComponentSectionsFacilities | ComponentSectionsFaqCategories | ComponentSectionsFaqs | ComponentSectionsFileList | ComponentSectionsGallery | ComponentSectionsIframe | ComponentSectionsInbaLatestRelease | ComponentSectionsInbaReleases | ComponentSectionsLinks | ComponentSectionsNarrowText | ComponentSectionsNewsletter | ComponentSectionsNumbersOverview | ComponentSectionsNumericalList | ComponentSectionsOfficialBoard | ComponentSectionsOpeningHours | ComponentSectionsOrganizationalStructure | ComponentSectionsPartners | ComponentSectionsProsAndConsSection | ComponentSectionsRegulations | ComponentSectionsStarzLandingPage | ComponentSectionsTextWithImage | ComponentSectionsTextWithImageOverlapped | ComponentSectionsTootootEvents | ComponentSectionsVideos | Error

scalar PageSectionsDynamicZoneInput

union PageSidebarDynamicZone = ComponentSidebarsEmptySidebar | Error

scalar PageSidebarDynamicZoneInput

type Pagination {
  page: Int!
  pageCount: Int!
  pageSize: Int!
  total: Int!
}

input PaginationArg {
  limit: Int
  page: Int
  pageSize: Int
  start: Int
}

enum PublicationStatus {
  DRAFT
  PUBLISHED
}

type Query {
  adminGroup(documentId: ID!, status: PublicationStatus = PUBLISHED): AdminGroup
  adminGroups(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [AdminGroup]!
  adminGroups_connection(filters: AdminGroupFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): AdminGroupEntityResponseCollection
  alert(
    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Alert
  article(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Article
  articleCategories(
    filters: ArticleCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [ArticleCategory]!
  articleCategories_connection(
    filters: ArticleCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): ArticleCategoryEntityResponseCollection
  articleCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): ArticleCategory
  articles(
    filters: ArticleFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [Article]!
  articles_connection(
    filters: ArticleFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): ArticleEntityResponseCollection
  document(documentId: ID!, status: PublicationStatus = PUBLISHED): Document
  documentCategories(filters: DocumentCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [DocumentCategory]!
  documentCategories_connection(filters: DocumentCategoryFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): DocumentCategoryEntityResponseCollection
  documentCategory(documentId: ID!, status: PublicationStatus = PUBLISHED): DocumentCategory
  documents(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Document]!
  documents_connection(filters: DocumentFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): DocumentEntityResponseCollection
  faq(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Faq
  faqCategories(
    filters: FaqCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [FaqCategory]!
  faqCategories_connection(
    filters: FaqCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): FaqCategoryEntityResponseCollection
  faqCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): FaqCategory
  faqs(
    filters: FaqFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [Faq]!
  faqs_connection(
    filters: FaqFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): FaqEntityResponseCollection
  footer(
    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Footer
  general(
    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): General
  homepage(
    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Homepage
  i18NLocale(documentId: ID!, status: PublicationStatus = PUBLISHED): I18NLocale
  i18NLocales(filters: I18NLocaleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [I18NLocale]!
  i18NLocales_connection(filters: I18NLocaleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): I18NLocaleEntityResponseCollection
  inbaArticle(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaArticle
  inbaArticles(
    filters: InbaArticleFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [InbaArticle]!
  inbaArticles_connection(
    filters: InbaArticleFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): InbaArticleEntityResponseCollection
  inbaRelease(documentId: ID!, status: PublicationStatus = PUBLISHED): InbaRelease
  inbaReleases(filters: InbaReleaseFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [InbaRelease]!
  inbaReleases_connection(filters: InbaReleaseFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): InbaReleaseEntityResponseCollection
  inbaTag(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): InbaTag
  inbaTags(
    filters: InbaTagFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [InbaTag]!
  inbaTags_connection(
    filters: InbaTagFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): InbaTagEntityResponseCollection
  internalJob(documentId: ID!, status: PublicationStatus = PUBLISHED): InternalJob
  internalJobs(filters: InternalJobFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [InternalJob]!
  internalJobs_connection(filters: InternalJobFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): InternalJobEntityResponseCollection
  me: UsersPermissionsMe
  menu(
    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Menu
  page(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Page
  pageCategories(
    filters: PageCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [PageCategory]!
  pageCategories_connection(
    filters: PageCategoryFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): PageCategoryEntityResponseCollection
  pageCategory(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): PageCategory
  pages(
    filters: PageFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [Page]!
  pages_connection(
    filters: PageFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): PageEntityResponseCollection
  redirect(documentId: ID!, status: PublicationStatus = PUBLISHED): Redirect
  redirects(filters: RedirectFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Redirect]!
  redirects_connection(filters: RedirectFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): RedirectEntityResponseCollection
  regulation(documentId: ID!, status: PublicationStatus = PUBLISHED): Regulation
  regulations(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [Regulation]!
  regulations_connection(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): RegulationEntityResponseCollection
  reviewWorkflowsWorkflow(documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflow
  reviewWorkflowsWorkflowStage(documentId: ID!, status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStage
  reviewWorkflowsWorkflowStages(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [ReviewWorkflowsWorkflowStage]!
  reviewWorkflowsWorkflowStages_connection(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowStageEntityResponseCollection
  reviewWorkflowsWorkflows(filters: ReviewWorkflowsWorkflowFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [ReviewWorkflowsWorkflow]!
  reviewWorkflowsWorkflows_connection(filters: ReviewWorkflowsWorkflowFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): ReviewWorkflowsWorkflowEntityResponseCollection
  tag(
    documentId: ID!

    """The locale to use for the query"""
    locale: I18NLocaleCode
    status: PublicationStatus = PUBLISHED
  ): Tag
  tags(
    filters: TagFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): [Tag]!
  tags_connection(
    filters: TagFiltersInput

    """The locale to use for the query"""
    locale: I18NLocaleCode
    pagination: PaginationArg = {}
    sort: [String] = []
    status: PublicationStatus = PUBLISHED
  ): TagEntityResponseCollection
  taxAdministratorsList(status: PublicationStatus = PUBLISHED): TaxAdministratorsList
  uploadFile(documentId: ID!, status: PublicationStatus = PUBLISHED): UploadFile
  uploadFiles(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UploadFile]!
  uploadFiles_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UploadFileEntityResponseCollection
  usersPermissionsRole(documentId: ID!, status: PublicationStatus = PUBLISHED): UsersPermissionsRole
  usersPermissionsRoles(filters: UsersPermissionsRoleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UsersPermissionsRole]!
  usersPermissionsRoles_connection(filters: UsersPermissionsRoleFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UsersPermissionsRoleEntityResponseCollection
  usersPermissionsUser(documentId: ID!, status: PublicationStatus = PUBLISHED): UsersPermissionsUser
  usersPermissionsUsers(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): [UsersPermissionsUser]!
  usersPermissionsUsers_connection(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = [], status: PublicationStatus = PUBLISHED): UsersPermissionsUserEntityResponseCollection
}

type Redirect {
  createdAt: DateTime
  destination: String!
  documentId: ID!
  page: Page
  permanent: Boolean
  publishedAt: DateTime
  source: String!
  updatedAt: DateTime
}

type RedirectEntity {
  attributes: Redirect
  id: ID
}

type RedirectEntityResponse {
  data: Redirect
}

type RedirectEntityResponseCollection {
  nodes: [Redirect!]!
  pageInfo: Pagination!
}

input RedirectFiltersInput {
  and: [RedirectFiltersInput]
  createdAt: DateTimeFilterInput
  destination: StringFilterInput
  documentId: IDFilterInput
  not: RedirectFiltersInput
  or: [RedirectFiltersInput]
  page: PageFiltersInput
  permanent: BooleanFilterInput
  publishedAt: DateTimeFilterInput
  source: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input RedirectInput {
  destination: String
  page: ID
  permanent: Boolean
  publishedAt: DateTime
  source: String
}

type RedirectRelationResponseCollection {
  nodes: [Redirect!]!
}

type Regulation {
  amending(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Regulation]!
  amending_connection(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RegulationRelationResponseCollection
  amendments(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Regulation]!
  amendments_connection(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RegulationRelationResponseCollection
  attachments(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UploadFile]!
  attachments_connection(filters: UploadFileFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UploadFileRelationResponseCollection
  cancellation: Regulation
  cancelling(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Regulation]!
  cancelling_connection(filters: RegulationFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): RegulationRelationResponseCollection
  category: ENUM_REGULATION_CATEGORY!
  createdAt: DateTime
  documentId: ID!
  effectiveFrom: Date!
  fullTitle: String!
  isFullTextRegulation: Boolean!
  mainDocument: UploadFile!
  publishedAt: DateTime
  regNumber: String!
  slug: String!
  titleText: String
  updatedAt: DateTime
}

type RegulationEntity {
  attributes: Regulation
  id: ID
}

type RegulationEntityResponse {
  data: Regulation
}

type RegulationEntityResponseCollection {
  nodes: [Regulation!]!
  pageInfo: Pagination!
}

input RegulationFiltersInput {
  amending: RegulationFiltersInput
  amendments: RegulationFiltersInput
  and: [RegulationFiltersInput]
  cancellation: RegulationFiltersInput
  cancelling: RegulationFiltersInput
  category: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  effectiveFrom: DateFilterInput
  fullTitle: StringFilterInput
  isFullTextRegulation: BooleanFilterInput
  not: RegulationFiltersInput
  or: [RegulationFiltersInput]
  publishedAt: DateTimeFilterInput
  regNumber: StringFilterInput
  slug: StringFilterInput
  titleText: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input RegulationInput {
  amending: [ID]
  amendments: [ID]
  attachments: [ID]
  cancellation: ID
  cancelling: [ID]
  category: ENUM_REGULATION_CATEGORY
  effectiveFrom: Date
  fullTitle: String
  isFullTextRegulation: Boolean
  mainDocument: ID
  publishedAt: DateTime
  regNumber: String
  slug: String
  titleText: String
}

type RegulationRelationResponseCollection {
  nodes: [Regulation!]!
}

type ResponseCollectionMeta {
  pagination: Pagination!
}

type ReviewWorkflowsWorkflow {
  contentTypes: JSON!
  createdAt: DateTime
  documentId: ID!
  name: String!
  publishedAt: DateTime
  stageRequiredToPublish: ReviewWorkflowsWorkflowStage
  stages(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ReviewWorkflowsWorkflowStage]!
  stages_connection(filters: ReviewWorkflowsWorkflowStageFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ReviewWorkflowsWorkflowStageRelationResponseCollection
  updatedAt: DateTime
}

type ReviewWorkflowsWorkflowEntity {
  attributes: ReviewWorkflowsWorkflow
  id: ID
}

type ReviewWorkflowsWorkflowEntityResponse {
  data: ReviewWorkflowsWorkflow
}

type ReviewWorkflowsWorkflowEntityResponseCollection {
  nodes: [ReviewWorkflowsWorkflow!]!
  pageInfo: Pagination!
}

input ReviewWorkflowsWorkflowFiltersInput {
  and: [ReviewWorkflowsWorkflowFiltersInput]
  contentTypes: JSONFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ReviewWorkflowsWorkflowFiltersInput
  or: [ReviewWorkflowsWorkflowFiltersInput]
  publishedAt: DateTimeFilterInput
  stageRequiredToPublish: ReviewWorkflowsWorkflowStageFiltersInput
  stages: ReviewWorkflowsWorkflowStageFiltersInput
  updatedAt: DateTimeFilterInput
}

input ReviewWorkflowsWorkflowInput {
  contentTypes: JSON
  name: String
  publishedAt: DateTime
  stageRequiredToPublish: ID
  stages: [ID]
}

type ReviewWorkflowsWorkflowRelationResponseCollection {
  nodes: [ReviewWorkflowsWorkflow!]!
}

type ReviewWorkflowsWorkflowStage {
  color: String
  createdAt: DateTime
  documentId: ID!
  name: String
  publishedAt: DateTime
  updatedAt: DateTime
  workflow: ReviewWorkflowsWorkflow
}

type ReviewWorkflowsWorkflowStageEntity {
  attributes: ReviewWorkflowsWorkflowStage
  id: ID
}

type ReviewWorkflowsWorkflowStageEntityResponse {
  data: ReviewWorkflowsWorkflowStage
}

type ReviewWorkflowsWorkflowStageEntityResponseCollection {
  nodes: [ReviewWorkflowsWorkflowStage!]!
  pageInfo: Pagination!
}

input ReviewWorkflowsWorkflowStageFiltersInput {
  and: [ReviewWorkflowsWorkflowStageFiltersInput]
  color: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: ReviewWorkflowsWorkflowStageFiltersInput
  or: [ReviewWorkflowsWorkflowStageFiltersInput]
  publishedAt: DateTimeFilterInput
  updatedAt: DateTimeFilterInput
  workflow: ReviewWorkflowsWorkflowFiltersInput
}

input ReviewWorkflowsWorkflowStageInput {
  color: String
  name: String
  publishedAt: DateTime
  workflow: ID
}

type ReviewWorkflowsWorkflowStageRelationResponseCollection {
  nodes: [ReviewWorkflowsWorkflowStage!]!
}

input StringFilterInput {
  and: [String]
  between: [String]
  contains: String
  containsi: String
  endsWith: String
  eq: String
  eqi: String
  gt: String
  gte: String
  in: [String]
  lt: String
  lte: String
  ne: String
  nei: String
  not: StringFilterInput
  notContains: String
  notContainsi: String
  notIn: [String]
  notNull: Boolean
  null: Boolean
  or: [String]
  startsWith: String
}

type Tag {
  articles(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Article]!
  articles_connection(filters: ArticleFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): ArticleRelationResponseCollection
  createdAt: DateTime
  documentId: ID!
  locale: String
  localizations(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [Tag]!
  localizations_connection(filters: TagFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): TagRelationResponseCollection
  pageCategory: PageCategory
  publishedAt: DateTime
  slug: String!
  title: String!
  updatedAt: DateTime
}

type TagEntity {
  attributes: Tag
  id: ID
}

type TagEntityResponse {
  data: Tag
}

type TagEntityResponseCollection {
  nodes: [Tag!]!
  pageInfo: Pagination!
}

input TagFiltersInput {
  and: [TagFiltersInput]
  articles: ArticleFiltersInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  locale: StringFilterInput
  localizations: TagFiltersInput
  not: TagFiltersInput
  or: [TagFiltersInput]
  pageCategory: PageCategoryFiltersInput
  publishedAt: DateTimeFilterInput
  slug: StringFilterInput
  title: StringFilterInput
  updatedAt: DateTimeFilterInput
}

input TagInput {
  articles: [ID]
  pageCategory: ID
  publishedAt: DateTime
  slug: String
  title: String
}

type TagRelationResponseCollection {
  nodes: [Tag!]!
}

type TaxAdministratorsList {
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  taxAdministrators(filters: ComponentTaxAdministratorsTaxAdministratorFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [ComponentTaxAdministratorsTaxAdministrator]!
  updatedAt: DateTime
}

type TaxAdministratorsListEntity {
  attributes: TaxAdministratorsList
  id: ID
}

type TaxAdministratorsListEntityResponse {
  data: TaxAdministratorsList
}

type TaxAdministratorsListEntityResponseCollection {
  nodes: [TaxAdministratorsList!]!
  pageInfo: Pagination!
}

input TaxAdministratorsListFiltersInput {
  and: [TaxAdministratorsListFiltersInput]
  createdAt: DateTimeFilterInput
  not: TaxAdministratorsListFiltersInput
  or: [TaxAdministratorsListFiltersInput]
  publishedAt: DateTimeFilterInput
  taxAdministrators: ComponentTaxAdministratorsTaxAdministratorFiltersInput
  updatedAt: DateTimeFilterInput
}

input TaxAdministratorsListInput {
  publishedAt: DateTime
  taxAdministrators: [ComponentTaxAdministratorsTaxAdministratorInput]
}

type TaxAdministratorsListRelationResponseCollection {
  nodes: [TaxAdministratorsList!]!
}

"""A time string with format HH:mm:ss.SSS"""
scalar Time

input TimeFilterInput {
  and: [Time]
  between: [Time]
  contains: Time
  containsi: Time
  endsWith: Time
  eq: Time
  eqi: Time
  gt: Time
  gte: Time
  in: [Time]
  lt: Time
  lte: Time
  ne: Time
  nei: Time
  not: TimeFilterInput
  notContains: Time
  notContainsi: Time
  notIn: [Time]
  notNull: Boolean
  null: Boolean
  or: [Time]
  startsWith: Time
}

type UploadFile {
  alternativeText: String
  caption: String
  createdAt: DateTime
  documentId: ID!
  ext: String
  formats: JSON
  hash: String!
  height: Int
  mime: String!
  name: String!
  previewUrl: String
  provider: String!
  provider_metadata: JSON
  publishedAt: DateTime
  related: [GenericMorph]
  size: Float!
  updatedAt: DateTime
  url: String!
  width: Int
}

type UploadFileEntity {
  attributes: UploadFile
  id: ID
}

type UploadFileEntityResponse {
  data: UploadFile
}

type UploadFileEntityResponseCollection {
  nodes: [UploadFile!]!
  pageInfo: Pagination!
}

input UploadFileFiltersInput {
  alternativeText: StringFilterInput
  and: [UploadFileFiltersInput]
  caption: StringFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  ext: StringFilterInput
  formats: JSONFilterInput
  hash: StringFilterInput
  height: IntFilterInput
  mime: StringFilterInput
  name: StringFilterInput
  not: UploadFileFiltersInput
  or: [UploadFileFiltersInput]
  previewUrl: StringFilterInput
  provider: StringFilterInput
  provider_metadata: JSONFilterInput
  publishedAt: DateTimeFilterInput
  size: FloatFilterInput
  updatedAt: DateTimeFilterInput
  url: StringFilterInput
  width: IntFilterInput
}

input UploadFileInput {
  alternativeText: String
  caption: String
  ext: String
  formats: JSON
  hash: String
  height: Int
  mime: String
  name: String
  previewUrl: String
  provider: String
  provider_metadata: JSON
  publishedAt: DateTime
  size: Float
  url: String
  width: Int
}

type UploadFileRelationResponseCollection {
  nodes: [UploadFile!]!
}

type UsersPermissionsCreateRolePayload {
  ok: Boolean!
}

type UsersPermissionsDeleteRolePayload {
  ok: Boolean!
}

input UsersPermissionsLoginInput {
  identifier: String!
  password: String!
  provider: String! = "local"
}

type UsersPermissionsLoginPayload {
  jwt: String
  user: UsersPermissionsMe!
}

type UsersPermissionsMe {
  blocked: Boolean
  confirmed: Boolean
  documentId: ID!
  email: String
  id: ID!
  role: UsersPermissionsMeRole
  username: String!
}

type UsersPermissionsMeRole {
  description: String
  id: ID!
  name: String!
  type: String
}

type UsersPermissionsPasswordPayload {
  ok: Boolean!
}

type UsersPermissionsPermission {
  action: String!
  createdAt: DateTime
  documentId: ID!
  publishedAt: DateTime
  role: UsersPermissionsRole
  updatedAt: DateTime
}

type UsersPermissionsPermissionEntity {
  attributes: UsersPermissionsPermission
  id: ID
}

type UsersPermissionsPermissionEntityResponse {
  data: UsersPermissionsPermission
}

type UsersPermissionsPermissionEntityResponseCollection {
  nodes: [UsersPermissionsPermission!]!
  pageInfo: Pagination!
}

input UsersPermissionsPermissionFiltersInput {
  action: StringFilterInput
  and: [UsersPermissionsPermissionFiltersInput]
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  not: UsersPermissionsPermissionFiltersInput
  or: [UsersPermissionsPermissionFiltersInput]
  publishedAt: DateTimeFilterInput
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
}

input UsersPermissionsPermissionInput {
  action: String
  publishedAt: DateTime
  role: ID
}

type UsersPermissionsPermissionRelationResponseCollection {
  nodes: [UsersPermissionsPermission!]!
}

input UsersPermissionsRegisterInput {
  email: String!
  password: String!
  username: String!
}

type UsersPermissionsRole {
  createdAt: DateTime
  description: String
  documentId: ID!
  name: String!
  permissions(filters: UsersPermissionsPermissionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UsersPermissionsPermission]!
  permissions_connection(filters: UsersPermissionsPermissionFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsPermissionRelationResponseCollection
  publishedAt: DateTime
  type: String
  updatedAt: DateTime
  users(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): [UsersPermissionsUser]!
  users_connection(filters: UsersPermissionsUserFiltersInput, pagination: PaginationArg = {}, sort: [String] = []): UsersPermissionsUserRelationResponseCollection
}

type UsersPermissionsRoleEntity {
  attributes: UsersPermissionsRole
  id: ID
}

type UsersPermissionsRoleEntityResponse {
  data: UsersPermissionsRole
}

type UsersPermissionsRoleEntityResponseCollection {
  nodes: [UsersPermissionsRole!]!
  pageInfo: Pagination!
}

input UsersPermissionsRoleFiltersInput {
  and: [UsersPermissionsRoleFiltersInput]
  createdAt: DateTimeFilterInput
  description: StringFilterInput
  documentId: IDFilterInput
  name: StringFilterInput
  not: UsersPermissionsRoleFiltersInput
  or: [UsersPermissionsRoleFiltersInput]
  permissions: UsersPermissionsPermissionFiltersInput
  publishedAt: DateTimeFilterInput
  type: StringFilterInput
  updatedAt: DateTimeFilterInput
  users: UsersPermissionsUserFiltersInput
}

input UsersPermissionsRoleInput {
  description: String
  name: String
  permissions: [ID]
  publishedAt: DateTime
  type: String
  users: [ID]
}

type UsersPermissionsRoleRelationResponseCollection {
  nodes: [UsersPermissionsRole!]!
}

type UsersPermissionsUpdateRolePayload {
  ok: Boolean!
}

type UsersPermissionsUser {
  blocked: Boolean
  confirmed: Boolean
  createdAt: DateTime
  documentId: ID!
  email: String!
  provider: String
  publishedAt: DateTime
  role: UsersPermissionsRole
  updatedAt: DateTime
  username: String!
}

type UsersPermissionsUserEntity {
  attributes: UsersPermissionsUser
  id: ID
}

type UsersPermissionsUserEntityResponse {
  data: UsersPermissionsUser
}

type UsersPermissionsUserEntityResponseCollection {
  nodes: [UsersPermissionsUser!]!
  pageInfo: Pagination!
}

input UsersPermissionsUserFiltersInput {
  and: [UsersPermissionsUserFiltersInput]
  blocked: BooleanFilterInput
  confirmed: BooleanFilterInput
  createdAt: DateTimeFilterInput
  documentId: IDFilterInput
  email: StringFilterInput
  not: UsersPermissionsUserFiltersInput
  or: [UsersPermissionsUserFiltersInput]
  provider: StringFilterInput
  publishedAt: DateTimeFilterInput
  role: UsersPermissionsRoleFiltersInput
  updatedAt: DateTimeFilterInput
  username: StringFilterInput
}

input UsersPermissionsUserInput {
  blocked: Boolean
  confirmed: Boolean
  email: String
  password: String
  provider: String
  publishedAt: DateTime
  role: ID
  username: String
}

type UsersPermissionsUserRelationResponseCollection {
  nodes: [UsersPermissionsUser!]!
}